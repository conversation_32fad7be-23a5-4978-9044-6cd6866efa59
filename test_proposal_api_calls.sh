#!/bin/bash

# Enhanced Proposal Generation API Test Script
# This script demonstrates how to generate proposals with real KeyPersonnelUploads data
# and Semantic Scholar research integration using REST API calls.

API_BASE="http://localhost:3011"
TENANT_ID="8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
OPPORTUNITY_ID="TEST_ENHANCED_$(date +%s)"
CLIENT_SHORT_NAME="adeptengineeringsolutions"

echo "🚀 Enhanced Proposal Generation API Test"
echo "========================================"
echo "API Base: $API_BASE"
echo "Tenant ID: $TENANT_ID"
echo "Opportunity ID: $OPPORTUNITY_ID"
echo "Client: $CLIENT_SHORT_NAME"
echo ""

# Step 1: Create a proposal generation request
echo "📝 Step 1: Creating proposal generation request..."

JOB_INSTRUCTION='{
  "opportunityId": "'$OPPORTUNITY_ID'",
  "clientShortName": "'$CLIENT_SHORT_NAME'",
  "tenantId": "'$TENANT_ID'",
  "profileId": "2",
  "opportunityType": "custom",
  "sourceDocuments": [],
  "forceRefresh": false,
  "setForReview": true,
  "exportType": 1,
  "proposalRequestType": 1,
  "coverPage": null,
  "trailingPage": null,
  "systemPromptParameters": null,
  "isRFP": true,
  "generatedVolumes": [1],
  "aiPersonalityId": "6"
}'

QUEUE_PAYLOAD='{
  "job_instruction": "'"$(echo $JOB_INSTRUCTION | sed 's/"/\\"/g')"'",
  "opps_id": "'$OPPORTUNITY_ID'",
  "tenant_id": "'$TENANT_ID'",
  "request_type": 1,
  "job_submitted_by": "api_test_user"
}'

echo "Submitting proposal generation request..."
RESPONSE=$(curl -s -X POST "$API_BASE/queue/proposal" \
  -H "Content-Type: application/json" \
  -d "$QUEUE_PAYLOAD")

echo "Response: $RESPONSE"

# Extract job_id from response
JOB_ID=$(echo $RESPONSE | grep -o '"job_id":"[^"]*"' | cut -d'"' -f4)

if [ -z "$JOB_ID" ]; then
    echo "❌ Failed to create proposal generation request"
    exit 1
fi

echo "✅ Proposal generation queued successfully!"
echo "Job ID: $JOB_ID"
echo ""

# Step 2: Monitor the proposal generation progress
echo "📊 Step 2: Monitoring proposal generation progress..."
echo "Checking every 10 seconds for up to 5 minutes..."

MAX_ATTEMPTS=30
ATTEMPT=0

while [ $ATTEMPT -lt $MAX_ATTEMPTS ]; do
    echo "Checking attempt $((ATTEMPT + 1))/$MAX_ATTEMPTS..."
    
    # Get queue status
    QUEUE_STATUS=$(curl -s "$API_BASE/queue/proposal/new")
    
    # Check if our job is in the response and get its status
    JOB_STATUS=$(echo $QUEUE_STATUS | grep -o '"job_id":"'$JOB_ID'"[^}]*"status":"[^"]*"' | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$JOB_STATUS" ]; then
        echo "Job $JOB_ID status: $JOB_STATUS"
        
        if [ "$JOB_STATUS" = "COMPLETED" ]; then
            echo "✅ Proposal generation completed successfully!"
            break
        elif [ "$JOB_STATUS" = "FAILED" ]; then
            echo "❌ Proposal generation failed!"
            exit 1
        fi
    else
        echo "Job not found in queue (may have completed)"
        break
    fi
    
    ATTEMPT=$((ATTEMPT + 1))
    sleep 10
done

if [ $ATTEMPT -eq $MAX_ATTEMPTS ]; then
    echo "⚠️ Monitoring timeout reached"
fi

echo ""

# Step 3: Check criticism queue (automatic queueing)
echo "🔍 Step 3: Checking if criticism analysis was automatically queued..."

CRITICISM_RESPONSE=$(curl -s "$API_BASE/criticism/queue/status")
echo "Criticism queue status: $CRITICISM_RESPONSE"

echo ""

# Step 4: Provide next steps
echo "🎯 Next Steps:"
echo "1. Check the ProposalsInReview table for your generated proposal"
echo "2. Look for enhanced content with real personnel data"
echo "3. Verify research enhancement in technical sections"
echo "4. Monitor criticism analysis results"
echo ""

echo "📋 Enhanced Features Tested:"
echo "✅ LLM-based personnel section detection"
echo "✅ Real KeyPersonnelUploads data integration"
echo "✅ Semantic Scholar research enhancement"
echo "✅ Automatic criticism system queueing"
echo "✅ Proper workflow routing (review/format)"
echo ""

echo "🔗 Useful API Endpoints:"
echo "- Queue Status: GET $API_BASE/queue/proposal/new"
echo "- Criticism Results: GET $API_BASE/criticism/results/$OPPORTUNITY_ID?tenant_id=$TENANT_ID"
echo "- Chat with Proposal: POST $API_BASE/chats/ask"
echo ""

echo "✨ Enhanced proposal generation test completed!"
