from datetime import datetime
from typing import List, Optional

from loguru import logger
from models.kontratar_models import SamOpportunityQueue
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

class SamOpportunityQueueController:
    @staticmethod
    async def get_new_queue_items(db: AsyncSession, limit: int = 10) -> List[SamOpportunityQueue]:
        """Get new SAM opportunity queue items (status = 'NEW')."""
        try:
            query = select(SamOpportunityQueue).where(
                SamOpportunityQueue.status == "NEW"
            ).order_by(SamOpportunityQueue.created_date.desc()).limit(limit)
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting new SAM opportunity queue items: {e}")
            return []

    @staticmethod
    async def update_queue_status(db: AsyncSession, opps_id: str, status: str) -> bool:
        """Update SAM opportunity queue status by opps_id."""
        try:
            query = update(SamOpportunityQueue).where(
                SamOpportunityQueue.opps_id == opps_id
            ).values(
                status=status,
                updated_date=datetime.utcnow()
            )
            result = await db.execute(query)
            await db.commit()
            logger.info(f"Updated SAM opportunity queue status for opps_id {opps_id} to {status}")
            return result.rowcount > 0
        except Exception as e:
            logger.error(f"Error updating SAM opportunity queue status: {e}")
            await db.rollback()
            return False 