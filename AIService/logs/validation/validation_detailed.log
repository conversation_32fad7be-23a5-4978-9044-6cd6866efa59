2025-08-05 12:11:06 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-05 12:11:07 | INFO     | VALIDATION | ProposalCriticismSchedulerService initialized
2025-08-05 12:11:40 | INFO     | VALIDATION | Database initialized successfully
2025-08-05 12:11:40 | INFO     | VALIDATION | Starting all schedulers...
2025-08-05 12:11:40 | INFO     | VALIDATION | Proposal scheduler started with 60 second interval
2025-08-05 12:11:40 | INFO     | VALIDATION | Custom opps scheduler started with 60 second interval
2025-08-05 12:11:40 | INFO     | VALIDATION | SAM opps scheduler started with 60 second interval
2025-08-05 12:11:40 | INFO     | VALIDATION | Client process queue scheduler started with 60 second interval
2025-08-05 12:11:40 | INFO     | VALIDATION | Datametastore queue scheduler started with 60 second interval
2025-08-05 12:11:40 | INFO     | VALIDATION | Simulation scheduler started with 60 second interval
2025-08-05 12:11:40 | INFO     | VALIDATION | All schedulers started
2025-08-05 12:11:40 | INFO     | VALIDATION | All schedulers started successfully
2025-08-05 12:11:40 | INFO     | VALIDATION | Schedulers are disabled on startup (via env variable)
2025-08-05 12:12:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:12:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:12:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:12:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:12:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:12:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:13:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:13:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:13:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:13:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:13:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:13:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:14:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:14:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:14:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:14:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:14:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:14:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:15:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:15:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:15:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:15:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:15:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:15:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:16:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:16:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:16:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:16:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:16:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:16:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:17:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:17:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:17:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:17:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:17:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:17:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:17:40 | INFO     | VALIDATION | Processing cover page with ID: 4195
2025-08-05 12:17:48 | INFO     | VALIDATION | Found cover page: cover-page-1751947116383.png
2025-08-05 12:17:48 | INFO     | VALIDATION | Using compliance settings for cover page: font=Times-Roman, body_size=12, header_size=14
2025-08-05 12:17:48 | INFO     | VALIDATION | Processing cover page with content type: image/png
2025-08-05 12:17:48 | INFO     | VALIDATION | Original image size: 1600 x 2000
2025-08-05 12:17:48 | INFO     | VALIDATION | Resized image to print quality: 2550 x 3300 pixels
2025-08-05 12:17:48 | INFO     | VALIDATION | Created image element to cover entire page with no text
2025-08-05 12:17:48 | INFO     | VALIDATION | Successfully created image cover page from: cover-page-1751947116383.png
2025-08-05 12:17:48 | INFO     | VALIDATION | Image-only cover page prepared: 1 elements
2025-08-05 12:17:48 | INFO     | VALIDATION | Converting draft with 5 sections
2025-08-05 12:17:48 | INFO     | VALIDATION | Processing section 1: <class 'dict'>
2025-08-05 12:17:48 | INFO     | VALIDATION | Section title: 1.0 Tab A - Proposal Cover/Transmittal Letter
2025-08-05 12:17:48 | INFO     | VALIDATION | Section content length: 1983 characters
2025-08-05 12:17:48 | INFO     | VALIDATION | Content preview: Adept Engineering Solutions
7130 Minstrel Way, Suite 210
Columbia, Maryland 21045
adeptengr.com

August 4, 2025

[Insert Government Agency Address Here - Not Provided in Context]

**Subject: Proposal ...
2025-08-05 12:17:48 | INFO     | VALIDATION | Processing section 2: <class 'dict'>
2025-08-05 12:17:48 | INFO     | VALIDATION | Section title: 2.0 Tab B - Factor 1 - Staffing & Key Personnel Qualifications
2025-08-05 12:17:48 | INFO     | VALIDATION | Section content length: 5167 characters
2025-08-05 12:17:48 | INFO     | VALIDATION | Content preview: Adept Engineering Solutions will assemble a highly qualified team dedicated to consistently delivering exceptional support for the Export Control Group (ECG). Our approach prioritizes proactive recrui...
2025-08-05 12:17:48 | INFO     | VALIDATION | Processing 4 subsections
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 1 title: 2.1 Recruitment, Hiring, and Retention Approach
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 1 content length: 5204 characters
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 2 title: 2.2 Certifications and Training Processes
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 2 content length: 4379 characters
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 3 title: 2.3 Resume of Proposed Key Personnel
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 3 content length: 3408 characters
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 4 title: 2.4 Tentative/Contingent Offer Letter
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 4 content length: 1749 characters
2025-08-05 12:17:48 | INFO     | VALIDATION | Processing section 3: <class 'dict'>
2025-08-05 12:17:48 | INFO     | VALIDATION | Section title: 3.0 Tab C - Factor 2 - Management Approach
2025-08-05 12:17:48 | INFO     | VALIDATION | Section content length: 4735 characters
2025-08-05 12:17:48 | INFO     | VALIDATION | Content preview: **Employee Turnover & Vacancy Mitigation**

Adept Engineering Solutions maintains a consistently low employee turnover rate of 8% across similar government contracts over the past three years. Average...
2025-08-05 12:17:48 | INFO     | VALIDATION | Processing 3 subsections
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 1 title: 3.1 Employee Turnover and Solutions
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 1 content length: 4838 characters
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 2 title: 3.2 Surge Support Availability
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 2 content length: 4157 characters
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 3 title: 3.3 Quality Control and Performance Monitoring
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 3 content length: 4689 characters
2025-08-05 12:17:48 | INFO     | VALIDATION | Processing section 4: <class 'dict'>
2025-08-05 12:17:48 | INFO     | VALIDATION | Section title: 4.0 Tab D - Factor 3 - Technical Approach
2025-08-05 12:17:48 | INFO     | VALIDATION | Section content length: 5626 characters
2025-08-05 12:17:48 | INFO     | VALIDATION | Content preview: Adept Engineering Solutions understands the critical importance of a robust Export Control Group (ECG) in ensuring compliance with evolving U.S. Export Control regulations (EAR, ITAR, and OFAC). Our a...
2025-08-05 12:17:48 | INFO     | VALIDATION | Processing 6 subsections
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 1 title: 4.1 TASK 1 – Program Management and Administration
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 1 content length: 5182 characters
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 2 title: 4.2 TASK 2 – Information Management
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 2 content length: 5641 characters
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 3 title: 4.3 TASK 3 – Program Compliance
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 3 content length: 5329 characters
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 4 title: 4.4 TASK 4 – Training and Outreach
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 4 content length: 5067 characters
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 5 title: 4.5 TASK 5 – Regulatory Support
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 5 content length: 5638 characters
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 6 title: 4.6 TASK 6 – Optional – Surge
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 6 content length: 5301 characters
2025-08-05 12:17:48 | INFO     | VALIDATION | Processing section 5: <class 'dict'>
2025-08-05 12:17:48 | INFO     | VALIDATION | Section title: 5.0 Tab E - Factor 4 - Demonstrated Corporate Experience
2025-08-05 12:17:48 | INFO     | VALIDATION | Section content length: 5596 characters
2025-08-05 12:17:48 | INFO     | VALIDATION | Content preview: **Project 1: DoD Export Control Support – Contract ABC-123 (2019 – 2023)**

Adept Engineering Solutions provided comprehensive export control support to the Defense Logistics Agency (DLA) under Contra...
2025-08-05 12:17:48 | INFO     | VALIDATION | Processing 3 subsections
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 1 title: 5.1 Experience Example 1
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 1 content length: 4136 characters
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 2 title: 5.2 Experience Example 2
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 2 content length: 3900 characters
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 3 title: 5.3 Experience Example 3
2025-08-05 12:17:48 | INFO     | VALIDATION | Subsection 3 content length: 3904 characters
2025-08-05 12:17:48 | INFO     | VALIDATION | Generated markdown with 96570 characters
2025-08-05 12:17:48 | INFO     | VALIDATION | Checking for TOC: opportunity_details=True, has_toc_text=True
2025-08-05 12:17:48 | INFO     | VALIDATION | TOC text found: [{"title": "Tab A - Proposal Cover/Transmittal Letter", "description": "Standard cover letter for th...
2025-08-05 12:17:48 | INFO     | VALIDATION | TOC JSON parsed successfully, 5 items
2025-08-05 12:17:48 | INFO     | VALIDATION | Content generated (length: 96570)
2025-08-05 12:17:48 | INFO     | VALIDATION | Using compliance formatting: margin=50pts, font=Times-Roman, body_size=12, header_size=14, footer_size=10, line_spacing=1.5
2025-08-05 12:17:48 | INFO     | VALIDATION | Processing cover page with 1 elements
2025-08-05 12:17:48 | INFO     | VALIDATION | Created templates for cover page and regular pages
2025-08-05 12:17:48 | INFO     | VALIDATION | Adding 1 cover page elements to PDF
2025-08-05 12:17:48 | INFO     | VALIDATION | Cover page added
2025-08-05 12:17:48 | INFO     | VALIDATION | Generating TOC with accurate page numbers
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Processing 817 lines of markdown content
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['KPI', 'Target', 'Measurement Frequency']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Time to Fill', '< 60 days', 'Monthly']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Cost Per Hire', '< $5,000', 'Quarterly']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Employee Retention Rate', '> 90%', 'Annually']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Employee Satisfaction Score', '> 4.0/5.0', 'Semi-Annually']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Splitting long bullet point (736 characters)
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Splitting long line (1076 characters)
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['KPI', 'Target', 'Measurement Frequency', 'Reporting Frequency']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Employee Turnover Rate', '<10%', 'Monthly', 'Quarterly']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Time to Fill Position', '<45 days', 'Monthly', 'Quarterly']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Employee Satisfaction', '>80%', 'Annually', 'Annually']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Training Completion Rate', '>90%', 'Monthly', 'Quarterly']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Surge Level**', '**Demand Increase**', '**Response Time**', '**Resource Allocation**', '**Example Scenario**']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Level 1 (Minor)**', '0-25%', 'Within 24 hours', 'Re-prioritization of existing tasks; overtime allocation', 'Increased license review requests due to a new regulation.']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Level 2 (Moderate)**', '26-50%', 'Within 12 hours', 'RRT activation; temporary reassignment of personnel from other projects.', 'Significant increase in commodity jurisdiction requests.']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Level 3 (Major)**', '51-100%', 'Within 6 hours', 'Full RRT activation; expedited recruitment of temporary personnel; potential project delays on lower-priority tasks.', 'Large-scale export control audit requiring immediate attention.']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Ending table with 4 rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table with 4 rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Processing 4 cleaned table rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Successfully added table with 4 rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**KPI**', '**Target**', '**Data Source**', '**Reporting Frequency**']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Error Rate (Deliverables)**', '< 2%', 'Peer Review Feedback, Government Feedback', 'Bi-Annually']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Rework Rate**', '< 5%', 'Tracking of revisions requested by Government', 'Bi-Annually']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**On-Time Delivery**', '100%', 'Project Schedule Tracking System', 'Bi-Annually']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Government Satisfaction**', '> 90%', 'Formal/Informal Feedback from FPMs', 'Bi-Annually']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Metric**', '**Frequency**', '**Reporting Method**', '**Recipient**']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Deliverable Accuracy', 'Weekly', 'Status Report', 'Client PM']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Response Time', 'Weekly', 'Status Report', 'Client PM']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Client Satisfaction', 'Quarterly', 'Formal Report', 'Client PM']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Corrective Action Status', 'Bi-Weekly', 'Status Report', 'Client PM']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Metric', 'Target', 'Data Source', 'Reporting Frequency']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Training Completion Rate', '95%', 'Training Records', 'Monthly']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Post-Training Assessment Score', '80% average', 'Post-Training Assessments', 'Monthly']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Number of Compliance Incidents', 'Reduction of 10% annually', 'Incident Reporting System', 'Quarterly']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Knowledge Base Usage', '50 unique users/month', 'Web Analytics', 'Monthly']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Assessment Area**', '**Compliance Criteria**', '**Scoring (1-5)**', '**Notes**']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Technology Control Plans', 'Completeness, Accuracy, Implementation', '1-5', 'Focus on adherence to EAR/ITAR']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Personnel Screening', 'Background checks, Training', '1-5', 'Verification of compliance with export control regulations']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Physical Security', 'Access controls, Safeguarding of sensitive information', '1-5', 'Assessment of physical security measures']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Export Documentation', 'Accuracy, Completeness, Compliance', '1-5', 'Review of shipping records and export licenses']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Surge Level**', '**Request Volume**', '**Dedicated SMEs**', '**Response Time (Initial)**', '**Deliverable Turnaround (Typical)**']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Level 1 (Low)', '1-2 Requests', '2-3 SMEs', '< 4 hours', '5-7 business days']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Level 2 (Medium)', '3-5 Requests', '5-7 SMEs', '< 8 hours', '3-5 business days']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Level 3 (High)', '6+ Requests', '8+ SMEs + Contract Staff', '< 12 hours', '1-3 business days']
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Ending table with 4 rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Adding table with 4 rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Processing 4 cleaned table rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Successfully added table with 4 rows
2025-08-05 12:17:48 | INFO     | VALIDATION | PDF Generator: Generated 526 ReportLab elements after processing 817 lines
2025-08-05 12:17:49 | INFO     | VALIDATION | Calculated 38 pages for main content
2025-08-05 12:17:49 | INFO     | VALIDATION | Added TOC entry: 1.0 Tab A - Proposal Cover/Transmittal Letter at page 3
2025-08-05 12:17:49 | INFO     | VALIDATION | Added TOC entry: 2.0 Tab B - Factor 1 - Staffing & Key Personnel Qualifications at page 10
2025-08-05 12:17:49 | INFO     | VALIDATION | Added TOC subsection: 2.1 Recruitment, Hiring, and Retention Approach at page 11
2025-08-05 12:17:49 | INFO     | VALIDATION | Added TOC subsection: 2.2 Certifications and Training Processes at page 12
2025-08-05 12:17:49 | INFO     | VALIDATION | Added TOC subsection: 2.3 Resume of Proposed Key Personnel at page 13
2025-08-05 12:17:49 | INFO     | VALIDATION | Added TOC subsection: 2.4 Tentative/Contingent Offer Letter at page 14
2025-08-05 12:17:49 | INFO     | VALIDATION | Added TOC entry: 3.0 Tab C - Factor 2 - Management Approach at page 17
2025-08-05 12:17:49 | INFO     | VALIDATION | Added TOC subsection: 3.1 Employee Turnover and Solutions at page 18
2025-08-05 12:17:49 | INFO     | VALIDATION | Added TOC subsection: 3.2 Surge Support Availability at page 19
2025-08-05 12:17:49 | INFO     | VALIDATION | Added TOC subsection: 3.3 Quality Control and Performance Monitoring at page 20
2025-08-05 12:17:49 | INFO     | VALIDATION | Added TOC entry: 4.0 Tab D - Factor 3 - Technical Approach at page 24
2025-08-05 12:17:49 | INFO     | VALIDATION | Added TOC subsection: 4.1 TASK 1 – Program Management and Administration at page 25
2025-08-05 12:17:49 | INFO     | VALIDATION | Added TOC subsection: 4.2 TASK 2 – Information Management at page 26
2025-08-05 12:17:49 | INFO     | VALIDATION | Added TOC subsection: 4.3 TASK 3 – Program Compliance at page 27
2025-08-05 12:17:49 | INFO     | VALIDATION | Added TOC subsection: 4.4 TASK 4 – Training and Outreach at page 28
2025-08-05 12:17:49 | INFO     | VALIDATION | Added TOC subsection: 4.5 TASK 5 – Regulatory Support at page 29
2025-08-05 12:17:49 | INFO     | VALIDATION | Added TOC subsection: 4.6 TASK 6 – Optional – Surge at page 30
2025-08-05 12:17:49 | INFO     | VALIDATION | Added TOC entry: 5.0 Tab E - Factor 4 - Demonstrated Corporate Experience at page 31
2025-08-05 12:17:49 | INFO     | VALIDATION | Added TOC subsection: 5.1 Experience Example 1 at page 32
2025-08-05 12:17:49 | INFO     | VALIDATION | Added TOC subsection: 5.2 Experience Example 2 at page 33
2025-08-05 12:17:49 | INFO     | VALIDATION | Added TOC subsection: 5.3 Experience Example 3 at page 34
2025-08-05 12:17:49 | INFO     | VALIDATION | Generated TOC with accurate page numbers using two-pass approach
2025-08-05 12:17:49 | INFO     | VALIDATION | TOC added after cover page
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Processing 817 lines of markdown content
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['KPI', 'Target', 'Measurement Frequency']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Time to Fill', '< 60 days', 'Monthly']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Cost Per Hire', '< $5,000', 'Quarterly']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Employee Retention Rate', '> 90%', 'Annually']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Employee Satisfaction Score', '> 4.0/5.0', 'Semi-Annually']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Splitting long bullet point (736 characters)
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Splitting long line (1076 characters)
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['KPI', 'Target', 'Measurement Frequency', 'Reporting Frequency']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Employee Turnover Rate', '<10%', 'Monthly', 'Quarterly']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Time to Fill Position', '<45 days', 'Monthly', 'Quarterly']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Employee Satisfaction', '>80%', 'Annually', 'Annually']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Training Completion Rate', '>90%', 'Monthly', 'Quarterly']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Surge Level**', '**Demand Increase**', '**Response Time**', '**Resource Allocation**', '**Example Scenario**']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Level 1 (Minor)**', '0-25%', 'Within 24 hours', 'Re-prioritization of existing tasks; overtime allocation', 'Increased license review requests due to a new regulation.']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Level 2 (Moderate)**', '26-50%', 'Within 12 hours', 'RRT activation; temporary reassignment of personnel from other projects.', 'Significant increase in commodity jurisdiction requests.']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Level 3 (Major)**', '51-100%', 'Within 6 hours', 'Full RRT activation; expedited recruitment of temporary personnel; potential project delays on lower-priority tasks.', 'Large-scale export control audit requiring immediate attention.']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Ending table with 4 rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table with 4 rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Processing 4 cleaned table rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Successfully added table with 4 rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**KPI**', '**Target**', '**Data Source**', '**Reporting Frequency**']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Error Rate (Deliverables)**', '< 2%', 'Peer Review Feedback, Government Feedback', 'Bi-Annually']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Rework Rate**', '< 5%', 'Tracking of revisions requested by Government', 'Bi-Annually']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**On-Time Delivery**', '100%', 'Project Schedule Tracking System', 'Bi-Annually']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Government Satisfaction**', '> 90%', 'Formal/Informal Feedback from FPMs', 'Bi-Annually']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Metric**', '**Frequency**', '**Reporting Method**', '**Recipient**']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Deliverable Accuracy', 'Weekly', 'Status Report', 'Client PM']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Response Time', 'Weekly', 'Status Report', 'Client PM']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Client Satisfaction', 'Quarterly', 'Formal Report', 'Client PM']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Corrective Action Status', 'Bi-Weekly', 'Status Report', 'Client PM']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Metric', 'Target', 'Data Source', 'Reporting Frequency']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Training Completion Rate', '95%', 'Training Records', 'Monthly']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Post-Training Assessment Score', '80% average', 'Post-Training Assessments', 'Monthly']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Number of Compliance Incidents', 'Reduction of 10% annually', 'Incident Reporting System', 'Quarterly']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Knowledge Base Usage', '50 unique users/month', 'Web Analytics', 'Monthly']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Assessment Area**', '**Compliance Criteria**', '**Scoring (1-5)**', '**Notes**']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Technology Control Plans', 'Completeness, Accuracy, Implementation', '1-5', 'Focus on adherence to EAR/ITAR']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Personnel Screening', 'Background checks, Training', '1-5', 'Verification of compliance with export control regulations']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Physical Security', 'Access controls, Safeguarding of sensitive information', '1-5', 'Assessment of physical security measures']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Export Documentation', 'Accuracy, Completeness, Compliance', '1-5', 'Review of shipping records and export licenses']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['**Surge Level**', '**Request Volume**', '**Dedicated SMEs**', '**Response Time (Initial)**', '**Deliverable Turnaround (Typical)**']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Level 1 (Low)', '1-2 Requests', '2-3 SMEs', '< 4 hours', '5-7 business days']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Level 2 (Medium)', '3-5 Requests', '5-7 SMEs', '< 8 hours', '3-5 business days']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Level 3 (High)', '6+ Requests', '8+ SMEs + Contract Staff', '< 12 hours', '1-3 business days']
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Ending table with 4 rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Adding table with 4 rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Processing 4 cleaned table rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Successfully added table with 4 rows
2025-08-05 12:17:49 | INFO     | VALIDATION | PDF Generator: Generated 526 ReportLab elements after processing 817 lines
2025-08-05 12:17:49 | INFO     | VALIDATION | Main content added
2025-08-05 12:17:50 | INFO     | VALIDATION | Drew full-page cover image: /tmp/tmp52weqjs3.png
2025-08-05 12:17:50 | INFO     | VALIDATION | PDF file successfully saved to: /home/<USER>/Desktop/Development/NEW/GovBD-BackEnd-Python/AIService/generated-pdfs/RFP_Draft_iRiYNgd8RC_8d9e9729-f7bd-44a0-9cf1-777f532a2db2_20250805_121748.pdf
2025-08-05 12:17:50 | INFO     | VALIDATION | Cleaned up temporary file: /tmp/tmp52weqjs3.png
2025-08-05 12:18:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:18:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:18:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:18:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:18:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:18:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:19:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:19:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:19:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:19:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:19:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:19:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:20:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:20:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:20:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:20:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:20:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:20:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:21:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:21:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:21:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:21:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:21:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:21:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:22:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:22:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:22:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:22:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:22:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:22:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:23:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:23:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:23:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:23:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:23:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:23:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:24:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:24:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:24:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:24:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:24:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:24:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:25:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:25:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:25:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:25:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:25:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:25:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:26:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:26:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:26:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:26:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:26:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:26:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:27:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:27:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:27:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:27:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:27:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:27:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:28:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:28:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:28:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:28:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:28:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:28:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:29:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:29:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:29:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:29:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:29:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:29:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:30:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:30:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:30:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:30:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:30:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:30:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:31:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:31:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:31:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:31:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:31:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:31:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:32:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:32:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:32:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:32:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:32:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:32:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:33:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:33:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:33:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:33:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:33:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:33:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:34:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:34:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:34:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:34:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:34:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:34:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:35:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:35:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:35:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:35:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:35:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:35:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:36:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:36:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:36:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:36:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:36:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:36:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:36:45 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-05 12:36:58 | INFO     | VALIDATION | DRAFT: Starting enhanced draft generation for opportunity iRiYNgd8RC
2025-08-05 12:36:58 | INFO     | VALIDATION | DRAFT: Processing 5 main sections
2025-08-05 12:36:58 | INFO     | VALIDATION | get_opportunity called with opportunity_id=iRiYNgd8RC, tenant_id=8d9e9729-f7bd-44a0-9cf1-777f532a2db2, source=custom
2025-08-05 12:36:58 | INFO     | VALIDATION | Searching CUSTOM for opportunity_id=iRiYNgd8RC
2025-08-05 12:37:04 | INFO     | VALIDATION | CUSTOM search result: {'title': 'Export Controls Group Support', 'description': 'OASIS+ Domain: Management  Advisory, CLIN X0101/NAICS Code 541611 Administrative Management \nand General Management Consulting Services', 'posted_date': datetime.datetime(2025, 6, 29, 0, 0), 'expiration_date': datetime.datetime(2025, 8, 31, 0, 0), 'naics_code': '541611', 'opportunity_type': 'Solicitation', 'classification_code': '70RSAT25R000000012', 'point_of_contact_first_name': 'Danette', 'point_of_contact_last_name': 'Willams', 'point_of_contact_email': '<EMAIL>', 'point_of_contact_phone': '2405488887', 'place_of_performance_city': 'Washington D.C.', 'place_of_performance_state': 'District of Columbia', 'place_of_performance_zip': '20706', 'place_of_performance_country': 'United States'}
2025-08-05 12:37:04 | INFO     | VALIDATION | Returning opportunity record: {'title': 'Export Controls Group Support', 'description': 'OASIS+ Domain: Management  Advisory, CLIN X0101/NAICS Code 541611 Administrative Management \nand General Management Consulting Services', 'posted_date': datetime.datetime(2025, 6, 29, 0, 0), 'expiration_date': datetime.datetime(2025, 8, 31, 0, 0), 'naics_code': '541611', 'opportunity_type': 'Solicitation', 'classification_code': '70RSAT25R000000012', 'point_of_contact_first_name': 'Danette', 'point_of_contact_last_name': 'Willams', 'point_of_contact_email': '<EMAIL>', 'point_of_contact_phone': '2405488887', 'place_of_performance_city': 'Washington D.C.', 'place_of_performance_state': 'District of Columbia', 'place_of_performance_zip': '20706', 'place_of_performance_country': 'United States'}
2025-08-05 12:37:04 | INFO     | VALIDATION | DRAFT: Processing main section 1/5: Tab A - Proposal Cover/Transmittal Letter
2025-08-05 12:37:04 | INFO     | VALIDATION | DRAFT: Processing section 1.0 - Tab A - Proposal Cover/Transmittal Letter
2025-08-05 12:37:04 | INFO     | VALIDATION | DRAFT: Cover letter detected: True
2025-08-05 12:37:04 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:37:04 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:37:04 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:37:04 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:37:04 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:37:04 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:37:04 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:37:04 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:37:04 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:37:04 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:37:04 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:37:15 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:37:15 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:37:15 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:37:18 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:37:18 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:37:18 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:37:18 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:37:18 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:37:18 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:37:18 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:37:18 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:37:18 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:37:18 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:37:18 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:37:19 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:37:19 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:37:19 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:37:21 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:37:21 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:37:21 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:37:21 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:37:21 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:37:21 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:37:21 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:37:21 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:37:21 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:37:21 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:37:21 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:37:21 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:37:21 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:37:21 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:37:25 | INFO     | VALIDATION | DRAFT: Retrieved 6 RFP context chunks
2025-08-05 12:37:25 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:37:25 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:37:25 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 12:37:25 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:37:25 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:37:25 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:37:25 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 12:37:25 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:37:25 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:37:25 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 12:37:25 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 12:37:25 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 12:37:25 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 12:37:25 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 12:37:28 | INFO     | VALIDATION | DRAFT: Retrieved 1 client context chunks
2025-08-05 12:37:28 | INFO     | VALIDATION | DRAFT: LLM attempt 1/3 for Tab A - Proposal Cover/Transmittal Letter
2025-08-05 12:37:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:37:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:37:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:37:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:37:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:37:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:37:58 | INFO     | VALIDATION | DRAFT: Processing cover letter content (preserving formatting)
2025-08-05 12:37:58 | INFO     | VALIDATION | DRAFT: Cover letter processed, length: 1943 chars
2025-08-05 12:37:58 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab A - Proposal Cover/Transmittal Letter: ['Cover letter contains placeholder: [insert']
2025-08-05 12:37:58 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 1: ['Cover letter contains placeholder: [insert']
2025-08-05 12:37:58 | INFO     | VALIDATION | DRAFT: LLM attempt 2/3 for Tab A - Proposal Cover/Transmittal Letter
2025-08-05 12:38:28 | INFO     | VALIDATION | DRAFT: Processing cover letter content (preserving formatting)
2025-08-05 12:38:28 | INFO     | VALIDATION | DRAFT: Cover letter processed, length: 1943 chars
2025-08-05 12:38:28 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab A - Proposal Cover/Transmittal Letter: ['Cover letter contains placeholder: [insert']
2025-08-05 12:38:28 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 2: ['Cover letter contains placeholder: [insert']
2025-08-05 12:38:28 | INFO     | VALIDATION | DRAFT: LLM attempt 3/3 for Tab A - Proposal Cover/Transmittal Letter
2025-08-05 12:38:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:38:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:38:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:38:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:38:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:38:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:39:16 | INFO     | VALIDATION | DRAFT: Processing cover letter content (preserving formatting)
2025-08-05 12:39:16 | INFO     | VALIDATION | DRAFT: Cover letter processed, length: 1943 chars
2025-08-05 12:39:16 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab A - Proposal Cover/Transmittal Letter: ['Cover letter contains placeholder: [insert']
2025-08-05 12:39:16 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 3: ['Cover letter contains placeholder: [insert']
2025-08-05 12:39:16 | WARNING  | VALIDATION | DRAFT: Using draft with validation warnings
2025-08-05 12:39:16 | INFO     | VALIDATION | DRAFT: Successfully generated draft for Tab A - Proposal Cover/Transmittal Letter (1943 chars)
2025-08-05 12:39:16 | INFO     | VALIDATION | DRAFT: Successfully processed section 1/5
2025-08-05 12:39:16 | INFO     | VALIDATION | DRAFT: Processing main section 2/5: Tab B - Factor 1 - Staffing & Key Personnel Qualifications
2025-08-05 12:39:16 | INFO     | VALIDATION | DRAFT: Processing section 2.0 - Tab B - Factor 1 - Staffing & Key Personnel Qualifications
2025-08-05 12:39:16 | INFO     | VALIDATION | DRAFT: Cover letter detected: False
2025-08-05 12:39:16 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:39:16 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:39:16 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:39:16 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:39:16 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:39:16 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:39:16 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:39:16 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:39:16 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:39:16 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:39:16 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:39:20 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:39:20 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:39:20 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:39:22 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:39:22 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:39:22 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:39:22 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:39:22 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:39:22 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:39:22 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:39:22 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:39:22 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:39:22 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:39:22 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:39:23 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:39:23 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:39:23 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:39:25 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:39:25 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:39:25 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:39:25 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:39:25 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:39:25 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:39:25 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:39:25 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:39:25 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:39:25 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:39:25 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:39:25 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:39:25 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:39:25 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:39:28 | INFO     | VALIDATION | DRAFT: Retrieved 6 RFP context chunks
2025-08-05 12:39:28 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:39:28 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:39:28 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 12:39:28 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:39:28 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:39:28 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:39:28 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 12:39:28 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:39:28 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:39:28 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 12:39:28 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 12:39:28 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 12:39:28 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 12:39:28 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 12:39:30 | INFO     | VALIDATION | DRAFT: Retrieved 1 client context chunks
2025-08-05 12:39:30 | INFO     | VALIDATION | DRAFT: LLM attempt 1/3 for Tab B - Factor 1 - Staffing & Key Personnel Qualifications
2025-08-05 12:39:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:39:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:39:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:39:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:39:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:39:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:40:15 | INFO     | VALIDATION | DRAFT: Processing technical section content
2025-08-05 12:40:15 | INFO     | VALIDATION | DRAFT: Technical section processed, length: 5487 chars
2025-08-05 12:40:15 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab B - Factor 1 - Staffing & Key Personnel Qualifications: ['Content may not be sufficiently relevant to section title']
2025-08-05 12:40:15 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 1: ['Content may not be sufficiently relevant to section title']
2025-08-05 12:40:15 | INFO     | VALIDATION | DRAFT: LLM attempt 2/3 for Tab B - Factor 1 - Staffing & Key Personnel Qualifications
2025-08-05 12:40:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:40:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:40:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:40:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:40:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:40:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:41:07 | INFO     | VALIDATION | DRAFT: Processing technical section content
2025-08-05 12:41:07 | INFO     | VALIDATION | DRAFT: Technical section processed, length: 5487 chars
2025-08-05 12:41:07 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab B - Factor 1 - Staffing & Key Personnel Qualifications: ['Content may not be sufficiently relevant to section title']
2025-08-05 12:41:07 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 2: ['Content may not be sufficiently relevant to section title']
2025-08-05 12:41:07 | INFO     | VALIDATION | DRAFT: LLM attempt 3/3 for Tab B - Factor 1 - Staffing & Key Personnel Qualifications
2025-08-05 12:41:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:41:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:41:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:41:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:41:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:41:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:42:10 | INFO     | VALIDATION | DRAFT: Processing technical section content
2025-08-05 12:42:10 | INFO     | VALIDATION | DRAFT: Technical section processed, length: 5487 chars
2025-08-05 12:42:10 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab B - Factor 1 - Staffing & Key Personnel Qualifications: ['Content may not be sufficiently relevant to section title']
2025-08-05 12:42:10 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 3: ['Content may not be sufficiently relevant to section title']
2025-08-05 12:42:10 | WARNING  | VALIDATION | DRAFT: Using draft with validation warnings
2025-08-05 12:42:10 | INFO     | VALIDATION | DRAFT: Successfully generated draft for Tab B - Factor 1 - Staffing & Key Personnel Qualifications (5487 chars)
2025-08-05 12:42:10 | INFO     | VALIDATION | DRAFT: Processing 4 subsections
2025-08-05 12:42:10 | INFO     | VALIDATION | DRAFT:   Processing section 2.1 - Recruitment, Hiring, and Retention Approach
2025-08-05 12:42:10 | INFO     | VALIDATION | DRAFT:   Cover letter detected: False
2025-08-05 12:42:10 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:42:10 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:42:10 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:42:10 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:42:10 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:42:10 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:42:10 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:42:10 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:42:10 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:42:10 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:42:10 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:42:14 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:42:14 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:42:14 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:42:17 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:42:17 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:42:17 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:42:17 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:42:17 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:42:17 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:42:17 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:42:17 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:42:17 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:42:17 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:42:17 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:42:18 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:42:18 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:42:18 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:42:22 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:42:22 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:42:22 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:42:22 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:42:22 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:42:22 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:42:22 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:42:22 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:42:22 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:42:22 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:42:22 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:42:23 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:42:23 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:42:23 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:42:26 | INFO     | VALIDATION | DRAFT:   Retrieved 6 RFP context chunks
2025-08-05 12:42:26 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:42:26 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:42:26 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 12:42:26 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:42:26 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:42:26 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:42:26 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 12:42:26 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:42:26 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:42:26 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 12:42:26 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 12:42:26 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 12:42:26 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 12:42:26 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 12:42:29 | INFO     | VALIDATION | DRAFT:   Retrieved 1 client context chunks
2025-08-05 12:42:29 | INFO     | VALIDATION | DRAFT:   LLM attempt 1/3 for Recruitment, Hiring, and Retention Approach
2025-08-05 12:42:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:42:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:42:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:42:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:42:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:42:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:43:04 | INFO     | VALIDATION | DRAFT:   Processing technical section content
2025-08-05 12:43:04 | INFO     | VALIDATION | DRAFT:   Technical section processed, length: 5538 chars
2025-08-05 12:43:04 | INFO     | VALIDATION | DRAFT:   Successfully generated draft for Recruitment, Hiring, and Retention Approach (5538 chars)
2025-08-05 12:43:04 | INFO     | VALIDATION | DRAFT:   Processing section 2.2 - Certifications and Training Processes
2025-08-05 12:43:04 | INFO     | VALIDATION | DRAFT:   Cover letter detected: False
2025-08-05 12:43:04 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:43:04 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:43:04 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:43:04 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:43:04 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:43:04 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:43:04 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:43:04 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:43:04 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:43:04 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:43:04 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:43:06 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:43:06 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:43:06 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:43:09 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:43:09 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:43:09 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:43:09 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:43:09 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:43:09 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:43:09 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:43:09 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:43:09 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:43:09 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:43:09 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:43:10 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:43:10 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:43:10 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:43:12 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:43:12 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:43:12 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:43:12 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:43:12 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:43:12 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:43:12 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:43:12 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:43:12 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:43:12 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:43:12 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:43:12 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:43:12 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:43:12 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:43:14 | INFO     | VALIDATION | DRAFT:   Retrieved 6 RFP context chunks
2025-08-05 12:43:14 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:43:14 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:43:14 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 12:43:14 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:43:14 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:43:14 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:43:14 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 12:43:14 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:43:14 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:43:14 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 12:43:14 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 12:43:15 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 12:43:15 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 12:43:15 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 12:43:18 | INFO     | VALIDATION | DRAFT:   Retrieved 1 client context chunks
2025-08-05 12:43:18 | INFO     | VALIDATION | DRAFT:   LLM attempt 1/3 for Certifications and Training Processes
2025-08-05 12:43:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:43:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:43:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:43:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:43:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:43:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:43:56 | INFO     | VALIDATION | DRAFT:   Processing technical section content
2025-08-05 12:43:56 | INFO     | VALIDATION | DRAFT:   Technical section processed, length: 4614 chars
2025-08-05 12:43:56 | INFO     | VALIDATION | DRAFT:   Successfully generated draft for Certifications and Training Processes (4614 chars)
2025-08-05 12:43:56 | INFO     | VALIDATION | DRAFT:   Processing section 2.3 - Resume of Proposed Key Personnel
2025-08-05 12:43:56 | INFO     | VALIDATION | DRAFT:   Cover letter detected: False
2025-08-05 12:43:56 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:43:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:43:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:43:56 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:43:56 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:43:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:43:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:43:56 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:43:56 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:43:56 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:43:56 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:43:58 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:43:58 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:43:58 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:44:00 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:44:00 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:44:00 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:44:00 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:44:00 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:44:00 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:44:00 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:44:00 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:44:00 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:44:00 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:44:00 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:44:00 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:44:00 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:44:00 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:44:03 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:44:03 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:44:03 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:44:03 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:44:03 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:44:03 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:44:03 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:44:03 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:44:03 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:44:03 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:44:03 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:44:03 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:44:03 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:44:03 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:44:06 | INFO     | VALIDATION | DRAFT:   Retrieved 6 RFP context chunks
2025-08-05 12:44:06 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:44:06 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:44:06 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 12:44:06 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:44:06 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:44:06 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:44:06 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 12:44:06 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:44:06 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:44:06 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 12:44:06 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 12:44:06 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 12:44:06 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 12:44:06 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 12:44:08 | INFO     | VALIDATION | DRAFT:   Retrieved 1 client context chunks
2025-08-05 12:44:08 | INFO     | VALIDATION | DRAFT:   LLM attempt 1/3 for Resume of Proposed Key Personnel
2025-08-05 12:44:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:44:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:44:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:44:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:44:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:44:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:45:15 | INFO     | VALIDATION | DRAFT:   Processing technical section content
2025-08-05 12:45:15 | INFO     | VALIDATION | DRAFT:   Technical section processed, length: 6714 chars
2025-08-05 12:45:15 | WARNING  | VALIDATION | DRAFT:   Quality validation failed for Resume of Proposed Key Personnel: ['Content may not be sufficiently relevant to section title']
2025-08-05 12:45:15 | WARNING  | VALIDATION | DRAFT:   Validation errors on attempt 1: ['Content may not be sufficiently relevant to section title']
2025-08-05 12:45:15 | INFO     | VALIDATION | DRAFT:   LLM attempt 2/3 for Resume of Proposed Key Personnel
2025-08-05 12:45:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:45:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:45:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:45:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:45:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:45:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:46:03 | INFO     | VALIDATION | DRAFT:   Processing technical section content
2025-08-05 12:46:03 | INFO     | VALIDATION | DRAFT:   Technical section processed, length: 6714 chars
2025-08-05 12:46:03 | WARNING  | VALIDATION | DRAFT:   Quality validation failed for Resume of Proposed Key Personnel: ['Content may not be sufficiently relevant to section title']
2025-08-05 12:46:03 | WARNING  | VALIDATION | DRAFT:   Validation errors on attempt 2: ['Content may not be sufficiently relevant to section title']
2025-08-05 12:46:03 | INFO     | VALIDATION | DRAFT:   LLM attempt 3/3 for Resume of Proposed Key Personnel
2025-08-05 12:46:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:46:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:46:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:46:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:46:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:46:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:46:55 | INFO     | VALIDATION | DRAFT:   Processing technical section content
2025-08-05 12:46:55 | INFO     | VALIDATION | DRAFT:   Technical section processed, length: 6714 chars
2025-08-05 12:46:55 | WARNING  | VALIDATION | DRAFT:   Quality validation failed for Resume of Proposed Key Personnel: ['Content may not be sufficiently relevant to section title']
2025-08-05 12:46:55 | WARNING  | VALIDATION | DRAFT:   Validation errors on attempt 3: ['Content may not be sufficiently relevant to section title']
2025-08-05 12:46:55 | WARNING  | VALIDATION | DRAFT:   Using draft with validation warnings
2025-08-05 12:46:55 | INFO     | VALIDATION | DRAFT:   Successfully generated draft for Resume of Proposed Key Personnel (6714 chars)
2025-08-05 12:46:55 | INFO     | VALIDATION | DRAFT:   Processing section 2.4 - Tentative/Contingent Offer Letter
2025-08-05 12:46:55 | INFO     | VALIDATION | DRAFT:   Cover letter detected: True
2025-08-05 12:46:55 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:46:55 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:46:55 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:46:55 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:46:55 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:46:55 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:46:55 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:46:55 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:46:55 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:46:55 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:46:55 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:46:56 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:46:56 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:46:56 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:46:58 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:46:58 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:46:58 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:46:58 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:46:58 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:46:58 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:46:58 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:46:58 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:46:58 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:46:58 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:46:58 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:46:59 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:46:59 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:46:59 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:47:01 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:47:01 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:47:01 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:47:01 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:47:01 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:47:01 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:47:01 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:47:01 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:47:01 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:47:01 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:47:01 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:47:01 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:47:01 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:47:01 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:47:04 | INFO     | VALIDATION | DRAFT:   Retrieved 6 RFP context chunks
2025-08-05 12:47:04 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:47:04 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:47:04 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 12:47:04 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:47:04 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:47:04 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:47:04 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 12:47:04 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:47:04 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:47:04 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 12:47:04 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 12:47:04 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 12:47:04 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 12:47:04 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 12:47:07 | INFO     | VALIDATION | DRAFT:   Retrieved 1 client context chunks
2025-08-05 12:47:07 | INFO     | VALIDATION | DRAFT:   LLM attempt 1/3 for Tentative/Contingent Offer Letter
2025-08-05 12:47:28 | INFO     | VALIDATION | DRAFT:   Processing cover letter content (preserving formatting)
2025-08-05 12:47:28 | INFO     | VALIDATION | DRAFT:   Cover letter processed, length: 1943 chars
2025-08-05 12:47:28 | WARNING  | VALIDATION | DRAFT:   Quality validation failed for Tentative/Contingent Offer Letter: ['Cover letter contains placeholder: [insert']
2025-08-05 12:47:28 | WARNING  | VALIDATION | DRAFT:   Validation errors on attempt 1: ['Cover letter contains placeholder: [insert']
2025-08-05 12:47:28 | INFO     | VALIDATION | DRAFT:   LLM attempt 2/3 for Tentative/Contingent Offer Letter
2025-08-05 12:47:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:47:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:47:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:47:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:47:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:47:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:48:06 | INFO     | VALIDATION | DRAFT:   Processing cover letter content (preserving formatting)
2025-08-05 12:48:06 | INFO     | VALIDATION | DRAFT:   Cover letter processed, length: 1943 chars
2025-08-05 12:48:06 | WARNING  | VALIDATION | DRAFT:   Quality validation failed for Tentative/Contingent Offer Letter: ['Cover letter contains placeholder: [insert']
2025-08-05 12:48:06 | WARNING  | VALIDATION | DRAFT:   Validation errors on attempt 2: ['Cover letter contains placeholder: [insert']
2025-08-05 12:48:06 | INFO     | VALIDATION | DRAFT:   LLM attempt 3/3 for Tentative/Contingent Offer Letter
2025-08-05 12:48:17 | INFO     | VALIDATION | DRAFT:   Processing cover letter content (preserving formatting)
2025-08-05 12:48:17 | INFO     | VALIDATION | DRAFT:   Cover letter processed, length: 1943 chars
2025-08-05 12:48:17 | WARNING  | VALIDATION | DRAFT:   Quality validation failed for Tentative/Contingent Offer Letter: ['Cover letter contains placeholder: [insert']
2025-08-05 12:48:17 | WARNING  | VALIDATION | DRAFT:   Validation errors on attempt 3: ['Cover letter contains placeholder: [insert']
2025-08-05 12:48:17 | WARNING  | VALIDATION | DRAFT:   Using draft with validation warnings
2025-08-05 12:48:17 | INFO     | VALIDATION | DRAFT:   Successfully generated draft for Tentative/Contingent Offer Letter (1943 chars)
2025-08-05 12:48:17 | INFO     | VALIDATION | DRAFT: Successfully processed section 2/5
2025-08-05 12:48:17 | INFO     | VALIDATION | DRAFT: Processing main section 3/5: Tab C - Factor 2 - Management Approach
2025-08-05 12:48:17 | INFO     | VALIDATION | DRAFT: Processing section 3.0 - Tab C - Factor 2 - Management Approach
2025-08-05 12:48:17 | INFO     | VALIDATION | DRAFT: Cover letter detected: False
2025-08-05 12:48:17 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:48:17 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:48:17 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:48:17 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:48:17 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:48:17 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:48:17 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:48:17 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:48:17 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:48:17 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:48:17 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:48:20 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:48:20 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:48:20 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:48:23 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:48:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:48:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:48:23 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:48:23 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:48:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:48:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:48:23 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:48:23 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:48:23 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:48:23 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:48:23 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:48:23 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:48:23 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:48:25 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:48:25 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:48:25 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:48:25 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:48:25 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:48:25 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:48:25 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:48:25 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:48:25 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:48:25 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:48:25 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:48:25 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:48:25 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:48:25 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:48:27 | INFO     | VALIDATION | DRAFT: Retrieved 6 RFP context chunks
2025-08-05 12:48:27 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:48:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:48:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 12:48:27 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:48:27 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:48:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:48:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 12:48:27 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:48:27 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:48:27 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 12:48:27 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 12:48:28 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 12:48:28 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 12:48:28 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 12:48:30 | INFO     | VALIDATION | DRAFT: Retrieved 1 client context chunks
2025-08-05 12:48:30 | INFO     | VALIDATION | DRAFT: LLM attempt 1/3 for Tab C - Factor 2 - Management Approach
2025-08-05 12:48:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:48:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:48:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:48:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:48:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:48:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:49:20 | INFO     | VALIDATION | DRAFT: Processing technical section content
2025-08-05 12:49:20 | INFO     | VALIDATION | DRAFT: Technical section processed, length: 4501 chars
2025-08-05 12:49:20 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab C - Factor 2 - Management Approach: ['Content may not be sufficiently relevant to section title']
2025-08-05 12:49:20 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 1: ['Content may not be sufficiently relevant to section title']
2025-08-05 12:49:20 | INFO     | VALIDATION | DRAFT: LLM attempt 2/3 for Tab C - Factor 2 - Management Approach
2025-08-05 12:49:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:49:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:49:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:49:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:49:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:49:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:49:57 | INFO     | VALIDATION | DRAFT: Processing technical section content
2025-08-05 12:49:57 | INFO     | VALIDATION | DRAFT: Technical section processed, length: 4501 chars
2025-08-05 12:49:57 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab C - Factor 2 - Management Approach: ['Content may not be sufficiently relevant to section title']
2025-08-05 12:49:57 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 2: ['Content may not be sufficiently relevant to section title']
2025-08-05 12:49:57 | INFO     | VALIDATION | DRAFT: LLM attempt 3/3 for Tab C - Factor 2 - Management Approach
2025-08-05 12:50:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:50:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:50:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:50:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:50:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:50:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:50:45 | INFO     | VALIDATION | DRAFT: Processing technical section content
2025-08-05 12:50:45 | INFO     | VALIDATION | DRAFT: Technical section processed, length: 4501 chars
2025-08-05 12:50:45 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab C - Factor 2 - Management Approach: ['Content may not be sufficiently relevant to section title']
2025-08-05 12:50:45 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 3: ['Content may not be sufficiently relevant to section title']
2025-08-05 12:50:45 | WARNING  | VALIDATION | DRAFT: Using draft with validation warnings
2025-08-05 12:50:45 | INFO     | VALIDATION | DRAFT: Successfully generated draft for Tab C - Factor 2 - Management Approach (4501 chars)
2025-08-05 12:50:45 | INFO     | VALIDATION | DRAFT: Processing 3 subsections
2025-08-05 12:50:45 | INFO     | VALIDATION | DRAFT:   Processing section 3.1 - Employee Turnover and Solutions
2025-08-05 12:50:45 | INFO     | VALIDATION | DRAFT:   Cover letter detected: False
2025-08-05 12:50:45 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:50:45 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:50:45 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:50:45 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:50:45 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:50:45 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:50:45 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:50:45 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:50:45 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:50:45 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:50:45 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:50:48 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:50:48 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:50:48 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:50:50 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:50:50 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:50:50 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:50:50 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:50:50 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:50:50 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:50:50 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:50:50 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:50:50 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:50:50 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:50:50 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:50:50 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:50:50 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:50:50 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:50:53 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:50:53 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:50:53 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:50:53 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:50:53 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:50:53 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:50:53 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:50:53 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:50:53 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:50:53 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:50:53 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:50:53 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:50:53 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:50:53 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:50:55 | INFO     | VALIDATION | DRAFT:   Retrieved 6 RFP context chunks
2025-08-05 12:50:55 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:50:55 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:50:55 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 12:50:55 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:50:55 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:50:55 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:50:55 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 12:50:55 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:50:55 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:50:55 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 12:50:55 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 12:50:55 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 12:50:55 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 12:50:55 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 12:50:57 | INFO     | VALIDATION | DRAFT:   Retrieved 1 client context chunks
2025-08-05 12:50:57 | INFO     | VALIDATION | DRAFT:   LLM attempt 1/3 for Employee Turnover and Solutions
2025-08-05 12:51:30 | INFO     | VALIDATION | DRAFT:   Processing technical section content
2025-08-05 12:51:30 | INFO     | VALIDATION | DRAFT:   Technical section processed, length: 4706 chars
2025-08-05 12:51:30 | INFO     | VALIDATION | DRAFT:   Successfully generated draft for Employee Turnover and Solutions (4706 chars)
2025-08-05 12:51:30 | INFO     | VALIDATION | DRAFT:   Processing section 3.2 - Surge Support Availability
2025-08-05 12:51:30 | INFO     | VALIDATION | DRAFT:   Cover letter detected: False
2025-08-05 12:51:30 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:51:30 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:51:30 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:51:30 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:51:30 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:51:30 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:51:30 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:51:30 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:51:30 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:51:30 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:51:30 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:51:34 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:51:34 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:51:34 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:51:37 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:51:37 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:51:37 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:51:37 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:51:37 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:51:37 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:51:37 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:51:37 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:51:37 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:51:37 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:51:37 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:51:37 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:51:37 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:51:37 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:51:39 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:51:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:51:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:51:39 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:51:39 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:51:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:51:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:51:39 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:51:39 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:51:39 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:51:39 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:51:40 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:51:40 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:51:40 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:51:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:51:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:51:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:51:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:51:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:51:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:51:42 | INFO     | VALIDATION | DRAFT:   Retrieved 6 RFP context chunks
2025-08-05 12:51:42 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:51:42 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:51:42 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 12:51:42 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:51:42 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:51:42 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:51:42 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 12:51:42 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:51:42 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:51:42 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 12:51:42 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 12:51:43 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 12:51:43 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 12:51:43 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 12:51:46 | INFO     | VALIDATION | DRAFT:   Retrieved 1 client context chunks
2025-08-05 12:51:46 | INFO     | VALIDATION | DRAFT:   LLM attempt 1/3 for Surge Support Availability
2025-08-05 12:52:15 | INFO     | VALIDATION | DRAFT:   Processing technical section content
2025-08-05 12:52:15 | INFO     | VALIDATION | DRAFT:   Technical section processed, length: 4407 chars
2025-08-05 12:52:15 | INFO     | VALIDATION | DRAFT:   Successfully generated draft for Surge Support Availability (4407 chars)
2025-08-05 12:52:15 | INFO     | VALIDATION | DRAFT:   Processing section 3.3 - Quality Control and Performance Monitoring
2025-08-05 12:52:15 | INFO     | VALIDATION | DRAFT:   Cover letter detected: False
2025-08-05 12:52:15 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:52:15 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:52:15 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:52:15 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:52:15 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:52:15 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:52:15 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:52:15 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:52:15 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:52:15 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:52:15 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:52:16 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:52:16 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:52:16 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:52:20 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:52:20 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:52:20 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:52:20 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:52:20 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:52:20 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:52:20 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:52:20 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:52:20 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:52:20 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:52:20 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:52:20 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:52:20 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:52:20 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:52:23 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:52:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:52:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:52:23 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:52:23 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:52:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:52:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:52:23 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:52:23 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:52:23 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:52:23 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:52:23 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:52:23 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:52:23 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:52:27 | INFO     | VALIDATION | DRAFT:   Retrieved 6 RFP context chunks
2025-08-05 12:52:27 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:52:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:52:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 12:52:27 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:52:27 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:52:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:52:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 12:52:27 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:52:27 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:52:27 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 12:52:27 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 12:52:27 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 12:52:27 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 12:52:27 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 12:52:31 | INFO     | VALIDATION | DRAFT:   Retrieved 1 client context chunks
2025-08-05 12:52:31 | INFO     | VALIDATION | DRAFT:   LLM attempt 1/3 for Quality Control and Performance Monitoring
2025-08-05 12:52:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:52:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:52:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:52:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:52:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:52:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:53:21 | INFO     | VALIDATION | DRAFT:   Processing technical section content
2025-08-05 12:53:21 | INFO     | VALIDATION | DRAFT:   Technical section processed, length: 5129 chars
2025-08-05 12:53:21 | INFO     | VALIDATION | DRAFT:   Successfully generated draft for Quality Control and Performance Monitoring (5129 chars)
2025-08-05 12:53:21 | INFO     | VALIDATION | DRAFT: Successfully processed section 3/5
2025-08-05 12:53:21 | INFO     | VALIDATION | DRAFT: Processing main section 4/5: Tab D - Factor 3 - Technical Approach
2025-08-05 12:53:21 | INFO     | VALIDATION | DRAFT: Processing section 4.0 - Tab D - Factor 3 - Technical Approach
2025-08-05 12:53:21 | INFO     | VALIDATION | DRAFT: Cover letter detected: False
2025-08-05 12:53:21 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:53:21 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:53:21 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:53:21 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:53:21 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:53:21 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:53:21 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:53:21 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:53:21 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:53:21 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:53:21 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:53:24 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:53:24 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:53:24 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:53:27 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:53:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:53:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:53:27 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:53:27 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:53:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:53:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:53:27 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:53:27 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:53:27 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:53:27 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:53:28 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:53:28 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:53:28 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:53:30 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:53:30 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:53:30 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:53:30 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:53:30 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:53:30 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:53:30 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:53:30 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:53:30 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:53:30 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:53:30 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:53:30 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:53:30 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:53:30 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:53:32 | INFO     | VALIDATION | DRAFT: Retrieved 6 RFP context chunks
2025-08-05 12:53:32 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:53:32 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:53:32 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 12:53:32 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:53:32 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:53:32 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:53:32 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 12:53:32 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:53:32 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:53:32 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 12:53:32 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 12:53:33 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 12:53:33 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 12:53:33 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 12:53:35 | INFO     | VALIDATION | DRAFT: Retrieved 1 client context chunks
2025-08-05 12:53:35 | INFO     | VALIDATION | DRAFT: LLM attempt 1/3 for Tab D - Factor 3 - Technical Approach
2025-08-05 12:53:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:53:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:53:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:53:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:53:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:53:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:54:11 | INFO     | VALIDATION | DRAFT: Processing technical section content
2025-08-05 12:54:11 | INFO     | VALIDATION | DRAFT: Technical section processed, length: 6040 chars
2025-08-05 12:54:11 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab D - Factor 3 - Technical Approach: ['Content may not be sufficiently relevant to section title']
2025-08-05 12:54:11 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 1: ['Content may not be sufficiently relevant to section title']
2025-08-05 12:54:11 | INFO     | VALIDATION | DRAFT: LLM attempt 2/3 for Tab D - Factor 3 - Technical Approach
2025-08-05 12:54:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:54:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:54:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:54:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:54:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:54:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:55:02 | INFO     | VALIDATION | DRAFT: Processing technical section content
2025-08-05 12:55:02 | INFO     | VALIDATION | DRAFT: Technical section processed, length: 6040 chars
2025-08-05 12:55:02 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab D - Factor 3 - Technical Approach: ['Content may not be sufficiently relevant to section title']
2025-08-05 12:55:02 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 2: ['Content may not be sufficiently relevant to section title']
2025-08-05 12:55:02 | INFO     | VALIDATION | DRAFT: LLM attempt 3/3 for Tab D - Factor 3 - Technical Approach
2025-08-05 12:55:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:55:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:55:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:55:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:55:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:55:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:55:56 | INFO     | VALIDATION | DRAFT: Processing technical section content
2025-08-05 12:55:56 | INFO     | VALIDATION | DRAFT: Technical section processed, length: 6040 chars
2025-08-05 12:55:56 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab D - Factor 3 - Technical Approach: ['Content may not be sufficiently relevant to section title']
2025-08-05 12:55:56 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 3: ['Content may not be sufficiently relevant to section title']
2025-08-05 12:55:56 | WARNING  | VALIDATION | DRAFT: Using draft with validation warnings
2025-08-05 12:55:56 | INFO     | VALIDATION | DRAFT: Successfully generated draft for Tab D - Factor 3 - Technical Approach (6040 chars)
2025-08-05 12:55:56 | INFO     | VALIDATION | DRAFT: Processing 6 subsections
2025-08-05 12:55:56 | INFO     | VALIDATION | DRAFT:   Processing section 4.1 - TASK 1 – Program Management and Administration
2025-08-05 12:55:56 | INFO     | VALIDATION | DRAFT:   Cover letter detected: False
2025-08-05 12:55:56 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:55:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:55:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:55:56 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:55:56 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:55:56 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:55:56 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:55:56 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:55:56 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:55:56 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:55:56 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:55:58 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:55:58 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:55:58 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:56:00 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:56:00 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:56:00 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:56:00 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:56:00 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:56:00 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:56:00 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:56:00 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:56:00 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:56:00 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:56:00 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:56:00 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:56:00 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:56:00 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:56:02 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:56:02 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:56:02 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:56:02 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:56:02 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:56:02 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:56:02 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 12:56:02 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:56:02 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 12:56:02 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 12:56:02 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:56:03 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 12:56:03 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 12:56:03 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 12:56:05 | INFO     | VALIDATION | DRAFT:   Retrieved 6 RFP context chunks
2025-08-05 12:56:05 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:56:05 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:56:05 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 12:56:05 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:56:05 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:56:05 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:56:05 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 12:56:05 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 12:56:05 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 12:56:05 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 12:56:05 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 12:56:05 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 12:56:05 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 12:56:05 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 12:56:08 | INFO     | VALIDATION | DRAFT:   Retrieved 1 client context chunks
2025-08-05 12:56:08 | INFO     | VALIDATION | DRAFT:   LLM attempt 1/3 for TASK 1 – Program Management and Administration
2025-08-05 12:56:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:56:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:56:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:56:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:56:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:56:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:57:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:57:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:57:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:57:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:57:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:57:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:58:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:58:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:58:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:58:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:58:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 12:58:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:59:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 12:59:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 12:59:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 12:59:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 12:59:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 12:59:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:00:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:00:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:00:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:00:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:00:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:00:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:01:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:01:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:01:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:01:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:01:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:01:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:02:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:02:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:02:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:02:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:02:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:02:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:03:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:03:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:03:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:03:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:03:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:03:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:04:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:04:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:04:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:04:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:04:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:04:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:04:51 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-05 13:05:02 | INFO     | VALIDATION | DRAFT: Starting enhanced draft generation for opportunity iRiYNgd8RC
2025-08-05 13:05:02 | INFO     | VALIDATION | DRAFT: Processing 5 main sections
2025-08-05 13:05:02 | INFO     | VALIDATION | get_opportunity called with opportunity_id=iRiYNgd8RC, tenant_id=8d9e9729-f7bd-44a0-9cf1-777f532a2db2, source=custom
2025-08-05 13:05:02 | INFO     | VALIDATION | Searching CUSTOM for opportunity_id=iRiYNgd8RC
2025-08-05 13:05:09 | INFO     | VALIDATION | CUSTOM search result: {'title': 'Export Controls Group Support', 'description': 'OASIS+ Domain: Management  Advisory, CLIN X0101/NAICS Code 541611 Administrative Management \nand General Management Consulting Services', 'posted_date': datetime.datetime(2025, 6, 29, 0, 0), 'expiration_date': datetime.datetime(2025, 8, 31, 0, 0), 'naics_code': '541611', 'opportunity_type': 'Solicitation', 'classification_code': '70RSAT25R000000012', 'point_of_contact_first_name': 'Danette', 'point_of_contact_last_name': 'Willams', 'point_of_contact_email': '<EMAIL>', 'point_of_contact_phone': '2405488887', 'place_of_performance_city': 'Washington D.C.', 'place_of_performance_state': 'District of Columbia', 'place_of_performance_zip': '20706', 'place_of_performance_country': 'United States'}
2025-08-05 13:05:09 | INFO     | VALIDATION | Returning opportunity record: {'title': 'Export Controls Group Support', 'description': 'OASIS+ Domain: Management  Advisory, CLIN X0101/NAICS Code 541611 Administrative Management \nand General Management Consulting Services', 'posted_date': datetime.datetime(2025, 6, 29, 0, 0), 'expiration_date': datetime.datetime(2025, 8, 31, 0, 0), 'naics_code': '541611', 'opportunity_type': 'Solicitation', 'classification_code': '70RSAT25R000000012', 'point_of_contact_first_name': 'Danette', 'point_of_contact_last_name': 'Willams', 'point_of_contact_email': '<EMAIL>', 'point_of_contact_phone': '2405488887', 'place_of_performance_city': 'Washington D.C.', 'place_of_performance_state': 'District of Columbia', 'place_of_performance_zip': '20706', 'place_of_performance_country': 'United States'}
2025-08-05 13:05:09 | INFO     | VALIDATION | DRAFT: Processing main section 1/5: Tab A - Proposal Cover/Transmittal Letter
2025-08-05 13:05:09 | INFO     | VALIDATION | DRAFT: Processing section 1.0 - Tab A - Proposal Cover/Transmittal Letter
2025-08-05 13:05:09 | INFO     | VALIDATION | DRAFT: Cover letter detected: True
2025-08-05 13:05:09 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:05:09 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:05:09 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:05:09 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:05:09 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:05:09 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:05:09 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:05:09 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:05:09 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:05:09 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:05:09 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:05:18 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:05:18 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:05:18 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:05:21 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:05:21 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:05:21 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:05:21 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:05:21 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:05:21 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:05:21 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:05:21 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:05:21 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:05:21 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:05:21 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:05:21 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:05:21 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:05:21 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:05:24 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:05:24 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:05:24 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:05:24 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:05:24 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:05:24 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:05:24 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:05:24 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:05:24 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:05:24 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:05:24 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:05:24 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:05:24 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:05:24 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:05:27 | INFO     | VALIDATION | DRAFT: Retrieved 6 RFP context chunks
2025-08-05 13:05:27 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:05:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:05:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:05:27 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:05:27 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:05:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:05:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:05:27 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:05:27 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:05:27 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 13:05:27 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:05:27 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 13:05:27 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 13:05:27 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:05:29 | INFO     | VALIDATION | DRAFT: Retrieved 1 client context chunks
2025-08-05 13:05:29 | INFO     | VALIDATION | DRAFT: LLM attempt 1/3 for Tab A - Proposal Cover/Transmittal Letter
2025-08-05 13:05:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:05:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:05:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:05:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:05:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:05:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:06:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:06:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:06:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:06:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:06:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:06:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:07:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:07:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:07:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:07:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:07:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:07:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:08:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:08:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:08:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:08:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:08:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:08:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:09:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:09:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:09:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:09:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:09:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:09:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:10:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:10:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:10:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:10:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:10:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:10:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:11:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:11:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:11:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:11:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:11:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:11:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:12:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:12:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:12:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:12:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:12:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:12:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:13:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:13:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:13:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:13:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:13:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:13:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:13:59 | INFO     | VALIDATION | DRAFT: Processing cover letter content (preserving formatting)
2025-08-05 13:13:59 | INFO     | VALIDATION | DRAFT: Cover letter processed, length: 1943 chars
2025-08-05 13:13:59 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab A - Proposal Cover/Transmittal Letter: ['Cover letter contains placeholder: [insert']
2025-08-05 13:13:59 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 1: ['Cover letter contains placeholder: [insert']
2025-08-05 13:13:59 | INFO     | VALIDATION | DRAFT: LLM attempt 2/3 for Tab A - Proposal Cover/Transmittal Letter
2025-08-05 13:14:34 | INFO     | VALIDATION | DRAFT: Processing cover letter content (preserving formatting)
2025-08-05 13:14:34 | INFO     | VALIDATION | DRAFT: Cover letter processed, length: 1943 chars
2025-08-05 13:14:34 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab A - Proposal Cover/Transmittal Letter: ['Cover letter contains placeholder: [insert']
2025-08-05 13:14:34 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 2: ['Cover letter contains placeholder: [insert']
2025-08-05 13:14:34 | INFO     | VALIDATION | DRAFT: LLM attempt 3/3 for Tab A - Proposal Cover/Transmittal Letter
2025-08-05 13:14:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:14:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:14:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:14:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:14:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:14:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:15:09 | INFO     | VALIDATION | DRAFT: Processing cover letter content (preserving formatting)
2025-08-05 13:15:09 | INFO     | VALIDATION | DRAFT: Cover letter processed, length: 1943 chars
2025-08-05 13:15:09 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab A - Proposal Cover/Transmittal Letter: ['Cover letter contains placeholder: [insert']
2025-08-05 13:15:09 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 3: ['Cover letter contains placeholder: [insert']
2025-08-05 13:15:09 | WARNING  | VALIDATION | DRAFT: Using draft with validation warnings
2025-08-05 13:15:09 | INFO     | VALIDATION | DRAFT: Successfully generated draft for Tab A - Proposal Cover/Transmittal Letter (1943 chars)
2025-08-05 13:15:09 | INFO     | VALIDATION | DRAFT: Successfully processed section 1/5
2025-08-05 13:15:09 | INFO     | VALIDATION | DRAFT: Processing main section 2/5: Tab B - Factor 1 - Staffing & Key Personnel Qualifications
2025-08-05 13:15:09 | INFO     | VALIDATION | DRAFT: Processing section 2.0 - Tab B - Factor 1 - Staffing & Key Personnel Qualifications
2025-08-05 13:15:09 | INFO     | VALIDATION | DRAFT: Cover letter detected: False
2025-08-05 13:15:09 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:15:09 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:15:09 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:15:09 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:15:09 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:15:09 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:15:09 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:15:09 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:15:09 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:15:09 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:15:09 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:15:15 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:15:15 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:15:15 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:15:17 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:15:17 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:15:17 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:15:17 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:15:17 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:15:17 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:15:17 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:15:17 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:15:17 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:15:17 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:15:17 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:15:17 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:15:17 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:15:17 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:15:19 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:15:19 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:15:19 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:15:19 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:15:19 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:15:19 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:15:19 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:15:19 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:15:19 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:15:19 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:15:19 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:15:20 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:15:20 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:15:20 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:15:22 | INFO     | VALIDATION | DRAFT: Retrieved 6 RFP context chunks
2025-08-05 13:15:22 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:15:22 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:15:22 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:15:22 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:15:22 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:15:22 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:15:22 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:15:22 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:15:22 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:15:22 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 13:15:22 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:15:22 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 13:15:22 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 13:15:22 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:15:24 | INFO     | VALIDATION | DRAFT: Retrieved 1 client context chunks
2025-08-05 13:15:24 | INFO     | VALIDATION | DRAFT: LLM attempt 1/3 for Tab B - Factor 1 - Staffing & Key Personnel Qualifications
2025-08-05 13:15:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:15:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:15:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:15:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:15:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:15:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:16:04 | INFO     | VALIDATION | DRAFT: Processing technical section content
2025-08-05 13:16:04 | INFO     | VALIDATION | DRAFT: Technical section processed, length: 5487 chars
2025-08-05 13:16:04 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab B - Factor 1 - Staffing & Key Personnel Qualifications: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:16:04 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 1: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:16:04 | INFO     | VALIDATION | DRAFT: LLM attempt 2/3 for Tab B - Factor 1 - Staffing & Key Personnel Qualifications
2025-08-05 13:16:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:16:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:16:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:16:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:16:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:16:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:16:49 | INFO     | VALIDATION | DRAFT: Processing technical section content
2025-08-05 13:16:49 | INFO     | VALIDATION | DRAFT: Technical section processed, length: 5487 chars
2025-08-05 13:16:49 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab B - Factor 1 - Staffing & Key Personnel Qualifications: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:16:49 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 2: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:16:49 | INFO     | VALIDATION | DRAFT: LLM attempt 3/3 for Tab B - Factor 1 - Staffing & Key Personnel Qualifications
2025-08-05 13:17:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:17:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:17:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:17:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:17:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:17:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:17:55 | INFO     | VALIDATION | DRAFT: Processing technical section content
2025-08-05 13:17:55 | INFO     | VALIDATION | DRAFT: Technical section processed, length: 5487 chars
2025-08-05 13:17:55 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab B - Factor 1 - Staffing & Key Personnel Qualifications: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:17:55 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 3: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:17:55 | WARNING  | VALIDATION | DRAFT: Using draft with validation warnings
2025-08-05 13:17:55 | INFO     | VALIDATION | DRAFT: Successfully generated draft for Tab B - Factor 1 - Staffing & Key Personnel Qualifications (5487 chars)
2025-08-05 13:17:55 | INFO     | VALIDATION | DRAFT: Processing 4 subsections
2025-08-05 13:17:55 | INFO     | VALIDATION | DRAFT:   Processing section 2.1 - Recruitment, Hiring, and Retention Approach
2025-08-05 13:17:55 | INFO     | VALIDATION | DRAFT:   Cover letter detected: False
2025-08-05 13:17:55 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:17:55 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:17:55 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:17:55 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:17:55 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:17:55 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:17:55 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:17:55 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:17:55 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:17:55 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:17:55 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:17:56 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:17:56 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:17:56 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:17:58 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:17:58 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:17:58 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:17:58 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:17:58 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:17:58 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:17:58 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:17:58 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:17:58 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:17:58 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:17:58 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:17:58 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:17:58 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:17:58 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:18:00 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:18:00 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:18:00 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:18:00 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:18:00 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:18:00 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:18:00 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:18:00 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:18:00 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:18:00 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:18:00 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:18:01 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:18:01 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:18:01 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:18:03 | INFO     | VALIDATION | DRAFT:   Retrieved 6 RFP context chunks
2025-08-05 13:18:03 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:18:03 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:18:03 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:18:03 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:18:03 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:18:03 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:18:03 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:18:03 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:18:03 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:18:03 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 13:18:03 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:18:03 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 13:18:03 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 13:18:03 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:18:06 | INFO     | VALIDATION | DRAFT:   Retrieved 1 client context chunks
2025-08-05 13:18:06 | INFO     | VALIDATION | DRAFT:   LLM attempt 1/3 for Recruitment, Hiring, and Retention Approach
2025-08-05 13:18:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:18:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:18:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:18:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:18:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:18:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:19:05 | INFO     | VALIDATION | DRAFT:   Processing technical section content
2025-08-05 13:19:05 | INFO     | VALIDATION | DRAFT:   Technical section processed, length: 5538 chars
2025-08-05 13:19:05 | INFO     | VALIDATION | DRAFT:   Successfully generated draft for Recruitment, Hiring, and Retention Approach (5538 chars)
2025-08-05 13:19:05 | INFO     | VALIDATION | DRAFT:   Processing section 2.2 - Certifications and Training Processes
2025-08-05 13:19:05 | INFO     | VALIDATION | DRAFT:   Cover letter detected: False
2025-08-05 13:19:05 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:19:05 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:19:05 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:19:05 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:19:05 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:19:05 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:19:05 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:19:05 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:19:05 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:19:05 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:19:05 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:19:07 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:19:07 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:19:07 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:19:10 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:19:10 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:19:10 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:19:10 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:19:10 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:19:10 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:19:10 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:19:10 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:19:10 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:19:10 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:19:10 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:19:11 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:19:11 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:19:11 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:19:16 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:19:16 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:19:16 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:19:16 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:19:16 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:19:16 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:19:16 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:19:16 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:19:16 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:19:16 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:19:16 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:19:16 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:19:16 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:19:16 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:19:23 | INFO     | VALIDATION | DRAFT:   Retrieved 6 RFP context chunks
2025-08-05 13:19:23 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:19:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:19:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:19:23 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:19:23 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:19:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:19:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:19:23 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:19:23 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:19:23 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 13:19:23 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:19:23 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 13:19:23 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 13:19:23 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:19:27 | INFO     | VALIDATION | DRAFT:   Retrieved 1 client context chunks
2025-08-05 13:19:27 | INFO     | VALIDATION | DRAFT:   LLM attempt 1/3 for Certifications and Training Processes
2025-08-05 13:19:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:19:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:19:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:19:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:19:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:19:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:20:14 | INFO     | VALIDATION | DRAFT:   Processing technical section content
2025-08-05 13:20:14 | INFO     | VALIDATION | DRAFT:   Technical section processed, length: 4614 chars
2025-08-05 13:20:14 | INFO     | VALIDATION | DRAFT:   Successfully generated draft for Certifications and Training Processes (4614 chars)
2025-08-05 13:20:14 | INFO     | VALIDATION | DRAFT:   Processing section 2.3 - Resume of Proposed Key Personnel
2025-08-05 13:20:14 | INFO     | VALIDATION | DRAFT:   Cover letter detected: False
2025-08-05 13:20:14 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:20:14 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:20:14 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:20:14 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:20:14 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:20:14 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:20:14 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:20:14 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:20:14 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:20:14 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:20:14 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:20:16 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:20:16 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:20:16 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:20:20 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:20:20 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:20:20 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:20:20 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:20:20 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:20:20 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:20:20 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:20:20 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:20:20 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:20:20 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:20:20 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:20:20 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:20:20 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:20:20 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:20:23 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:20:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:20:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:20:23 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:20:23 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:20:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:20:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:20:23 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:20:23 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:20:23 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:20:23 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:20:23 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:20:23 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:20:23 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:20:27 | INFO     | VALIDATION | DRAFT:   Retrieved 6 RFP context chunks
2025-08-05 13:20:27 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:20:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:20:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:20:27 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:20:27 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:20:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:20:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:20:27 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:20:27 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:20:27 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 13:20:27 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:20:28 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 13:20:28 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 13:20:28 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:20:35 | INFO     | VALIDATION | DRAFT:   Retrieved 1 client context chunks
2025-08-05 13:20:35 | INFO     | VALIDATION | DRAFT:   LLM attempt 1/3 for Resume of Proposed Key Personnel
2025-08-05 13:20:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:20:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:20:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:20:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:20:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:20:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:21:24 | INFO     | VALIDATION | DRAFT:   Processing technical section content
2025-08-05 13:21:24 | INFO     | VALIDATION | DRAFT:   Technical section processed, length: 6714 chars
2025-08-05 13:21:24 | WARNING  | VALIDATION | DRAFT:   Quality validation failed for Resume of Proposed Key Personnel: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:21:24 | WARNING  | VALIDATION | DRAFT:   Validation errors on attempt 1: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:21:24 | INFO     | VALIDATION | DRAFT:   LLM attempt 2/3 for Resume of Proposed Key Personnel
2025-08-05 13:21:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:21:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:21:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:21:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:21:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:21:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:22:32 | INFO     | VALIDATION | DRAFT:   Processing technical section content
2025-08-05 13:22:32 | INFO     | VALIDATION | DRAFT:   Technical section processed, length: 6714 chars
2025-08-05 13:22:32 | WARNING  | VALIDATION | DRAFT:   Quality validation failed for Resume of Proposed Key Personnel: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:22:32 | WARNING  | VALIDATION | DRAFT:   Validation errors on attempt 2: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:22:32 | INFO     | VALIDATION | DRAFT:   LLM attempt 3/3 for Resume of Proposed Key Personnel
2025-08-05 13:22:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:22:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:22:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:22:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:22:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:22:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:23:21 | INFO     | VALIDATION | DRAFT:   Processing technical section content
2025-08-05 13:23:21 | INFO     | VALIDATION | DRAFT:   Technical section processed, length: 6714 chars
2025-08-05 13:23:21 | WARNING  | VALIDATION | DRAFT:   Quality validation failed for Resume of Proposed Key Personnel: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:23:21 | WARNING  | VALIDATION | DRAFT:   Validation errors on attempt 3: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:23:21 | WARNING  | VALIDATION | DRAFT:   Using draft with validation warnings
2025-08-05 13:23:21 | INFO     | VALIDATION | DRAFT:   Successfully generated draft for Resume of Proposed Key Personnel (6714 chars)
2025-08-05 13:23:21 | INFO     | VALIDATION | DRAFT:   Processing section 2.4 - Tentative/Contingent Offer Letter
2025-08-05 13:23:21 | INFO     | VALIDATION | DRAFT:   Cover letter detected: True
2025-08-05 13:23:21 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:23:21 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:23:21 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:23:21 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:23:21 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:23:21 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:23:21 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:23:21 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:23:21 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:23:21 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:23:21 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:23:22 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:23:22 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:23:22 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:23:25 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:23:25 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:23:25 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:23:25 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:23:25 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:23:25 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:23:25 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:23:25 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:23:25 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:23:25 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:23:25 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:23:25 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:23:25 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:23:25 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:23:27 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:23:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:23:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:23:27 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:23:27 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:23:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:23:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:23:27 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:23:27 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:23:27 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:23:27 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:23:28 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:23:28 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:23:28 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:23:30 | INFO     | VALIDATION | DRAFT:   Retrieved 6 RFP context chunks
2025-08-05 13:23:30 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:23:30 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:23:30 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:23:30 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:23:30 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:23:30 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:23:30 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:23:30 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:23:30 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:23:30 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 13:23:30 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:23:30 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 13:23:30 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 13:23:30 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:23:33 | INFO     | VALIDATION | DRAFT:   Retrieved 1 client context chunks
2025-08-05 13:23:33 | INFO     | VALIDATION | DRAFT:   LLM attempt 1/3 for Tentative/Contingent Offer Letter
2025-08-05 13:23:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:23:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:23:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:23:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:23:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:23:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:24:07 | INFO     | VALIDATION | DRAFT:   Processing cover letter content (preserving formatting)
2025-08-05 13:24:07 | INFO     | VALIDATION | DRAFT:   Cover letter processed, length: 1943 chars
2025-08-05 13:24:07 | WARNING  | VALIDATION | DRAFT:   Quality validation failed for Tentative/Contingent Offer Letter: ['Cover letter contains placeholder: [insert']
2025-08-05 13:24:07 | WARNING  | VALIDATION | DRAFT:   Validation errors on attempt 1: ['Cover letter contains placeholder: [insert']
2025-08-05 13:24:07 | INFO     | VALIDATION | DRAFT:   LLM attempt 2/3 for Tentative/Contingent Offer Letter
2025-08-05 13:24:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:24:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:24:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:24:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:24:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:24:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:24:56 | INFO     | VALIDATION | DRAFT:   Processing cover letter content (preserving formatting)
2025-08-05 13:24:56 | INFO     | VALIDATION | DRAFT:   Cover letter processed, length: 1943 chars
2025-08-05 13:24:56 | WARNING  | VALIDATION | DRAFT:   Quality validation failed for Tentative/Contingent Offer Letter: ['Cover letter contains placeholder: [insert']
2025-08-05 13:24:56 | WARNING  | VALIDATION | DRAFT:   Validation errors on attempt 2: ['Cover letter contains placeholder: [insert']
2025-08-05 13:24:56 | INFO     | VALIDATION | DRAFT:   LLM attempt 3/3 for Tentative/Contingent Offer Letter
2025-08-05 13:25:33 | INFO     | VALIDATION | DRAFT:   Processing cover letter content (preserving formatting)
2025-08-05 13:25:33 | INFO     | VALIDATION | DRAFT:   Cover letter processed, length: 1943 chars
2025-08-05 13:25:33 | WARNING  | VALIDATION | DRAFT:   Quality validation failed for Tentative/Contingent Offer Letter: ['Cover letter contains placeholder: [insert']
2025-08-05 13:25:33 | WARNING  | VALIDATION | DRAFT:   Validation errors on attempt 3: ['Cover letter contains placeholder: [insert']
2025-08-05 13:25:33 | WARNING  | VALIDATION | DRAFT:   Using draft with validation warnings
2025-08-05 13:25:33 | INFO     | VALIDATION | DRAFT:   Successfully generated draft for Tentative/Contingent Offer Letter (1943 chars)
2025-08-05 13:25:33 | INFO     | VALIDATION | DRAFT: Successfully processed section 2/5
2025-08-05 13:25:33 | INFO     | VALIDATION | DRAFT: Processing main section 3/5: Tab C - Factor 2 - Management Approach
2025-08-05 13:25:33 | INFO     | VALIDATION | DRAFT: Processing section 3.0 - Tab C - Factor 2 - Management Approach
2025-08-05 13:25:33 | INFO     | VALIDATION | DRAFT: Cover letter detected: False
2025-08-05 13:25:33 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:25:33 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:25:33 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:25:33 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:25:33 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:25:33 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:25:33 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:25:33 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:25:33 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:25:33 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:25:33 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:25:35 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:25:35 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:25:35 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:25:37 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:25:37 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:25:37 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:25:37 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:25:37 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:25:37 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:25:37 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:25:37 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:25:37 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:25:37 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:25:37 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:25:37 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:25:37 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:25:37 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:25:39 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:25:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:25:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:25:39 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:25:39 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:25:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:25:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:25:39 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:25:39 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:25:39 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:25:39 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:25:40 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:25:40 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:25:40 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:25:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:25:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:25:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:25:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:25:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:25:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:25:42 | INFO     | VALIDATION | DRAFT: Retrieved 6 RFP context chunks
2025-08-05 13:25:42 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:25:42 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:25:42 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:25:42 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:25:42 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:25:42 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:25:42 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:25:42 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:25:42 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:25:42 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 13:25:42 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:25:42 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 13:25:42 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 13:25:42 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:25:44 | INFO     | VALIDATION | DRAFT: Retrieved 1 client context chunks
2025-08-05 13:25:44 | INFO     | VALIDATION | DRAFT: LLM attempt 1/3 for Tab C - Factor 2 - Management Approach
2025-08-05 13:26:21 | INFO     | VALIDATION | DRAFT: Processing technical section content
2025-08-05 13:26:21 | INFO     | VALIDATION | DRAFT: Technical section processed, length: 4501 chars
2025-08-05 13:26:21 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab C - Factor 2 - Management Approach: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:26:21 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 1: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:26:21 | INFO     | VALIDATION | DRAFT: LLM attempt 2/3 for Tab C - Factor 2 - Management Approach
2025-08-05 13:26:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:26:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:26:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:26:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:26:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:26:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:27:21 | INFO     | VALIDATION | DRAFT: Processing technical section content
2025-08-05 13:27:21 | INFO     | VALIDATION | DRAFT: Technical section processed, length: 4501 chars
2025-08-05 13:27:21 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab C - Factor 2 - Management Approach: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:27:21 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 2: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:27:21 | INFO     | VALIDATION | DRAFT: LLM attempt 3/3 for Tab C - Factor 2 - Management Approach
2025-08-05 13:27:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:27:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:27:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:27:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:27:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:27:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:27:59 | INFO     | VALIDATION | DRAFT: Processing technical section content
2025-08-05 13:27:59 | INFO     | VALIDATION | DRAFT: Technical section processed, length: 4501 chars
2025-08-05 13:27:59 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab C - Factor 2 - Management Approach: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:27:59 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 3: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:27:59 | WARNING  | VALIDATION | DRAFT: Using draft with validation warnings
2025-08-05 13:27:59 | INFO     | VALIDATION | DRAFT: Successfully generated draft for Tab C - Factor 2 - Management Approach (4501 chars)
2025-08-05 13:27:59 | INFO     | VALIDATION | DRAFT: Processing 3 subsections
2025-08-05 13:27:59 | INFO     | VALIDATION | DRAFT:   Processing section 3.1 - Employee Turnover and Solutions
2025-08-05 13:27:59 | INFO     | VALIDATION | DRAFT:   Cover letter detected: False
2025-08-05 13:27:59 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:27:59 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:27:59 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:27:59 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:27:59 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:27:59 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:27:59 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:27:59 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:27:59 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:27:59 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:27:59 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:28:01 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:28:01 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:28:01 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:28:03 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:03 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:03 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:28:03 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:03 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:03 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:03 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:28:03 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:28:03 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:03 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:28:03 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:28:03 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:28:03 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:28:03 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:28:06 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:06 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:06 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:28:06 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:06 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:06 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:06 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:28:06 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:28:06 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:06 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:28:06 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:28:06 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:28:06 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:28:06 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:28:08 | INFO     | VALIDATION | DRAFT:   Retrieved 6 RFP context chunks
2025-08-05 13:28:08 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:28:08 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:28:08 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:28:08 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:28:08 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:28:08 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:28:08 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:28:08 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:28:08 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:28:08 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 13:28:08 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:28:08 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 13:28:08 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 13:28:08 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:28:11 | INFO     | VALIDATION | DRAFT:   Retrieved 1 client context chunks
2025-08-05 13:28:11 | INFO     | VALIDATION | DRAFT:   LLM attempt 1/3 for Employee Turnover and Solutions
2025-08-05 13:28:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:28:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:28:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:28:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:28:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:28:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:28:47 | INFO     | VALIDATION | DRAFT:   Processing technical section content
2025-08-05 13:28:47 | INFO     | VALIDATION | DRAFT:   Technical section processed, length: 4706 chars
2025-08-05 13:28:47 | INFO     | VALIDATION | DRAFT:   Successfully generated draft for Employee Turnover and Solutions (4706 chars)
2025-08-05 13:28:47 | INFO     | VALIDATION | DRAFT:   Processing section 3.2 - Surge Support Availability
2025-08-05 13:28:47 | INFO     | VALIDATION | DRAFT:   Cover letter detected: False
2025-08-05 13:28:47 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:47 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:47 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:28:47 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:47 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:47 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:47 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:28:47 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:28:47 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:47 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:28:47 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:28:48 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:28:48 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:28:48 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:28:51 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:51 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:51 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:28:51 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:51 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:51 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:51 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:28:51 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:28:51 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:51 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:28:51 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:28:51 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:28:51 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:28:51 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:28:53 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:53 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:53 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:28:53 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:53 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:53 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:53 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:28:53 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:28:53 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:28:53 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:28:53 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:28:53 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:28:53 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:28:53 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:28:55 | INFO     | VALIDATION | DRAFT:   Retrieved 6 RFP context chunks
2025-08-05 13:28:55 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:28:55 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:28:55 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:28:55 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:28:55 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:28:55 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:28:55 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:28:55 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:28:55 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:28:55 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 13:28:55 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:28:55 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 13:28:55 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 13:28:55 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:28:57 | INFO     | VALIDATION | DRAFT:   Retrieved 1 client context chunks
2025-08-05 13:28:57 | INFO     | VALIDATION | DRAFT:   LLM attempt 1/3 for Surge Support Availability
2025-08-05 13:29:32 | INFO     | VALIDATION | DRAFT:   Processing technical section content
2025-08-05 13:29:32 | INFO     | VALIDATION | DRAFT:   Technical section processed, length: 4407 chars
2025-08-05 13:29:32 | INFO     | VALIDATION | DRAFT:   Successfully generated draft for Surge Support Availability (4407 chars)
2025-08-05 13:29:32 | INFO     | VALIDATION | DRAFT:   Processing section 3.3 - Quality Control and Performance Monitoring
2025-08-05 13:29:32 | INFO     | VALIDATION | DRAFT:   Cover letter detected: False
2025-08-05 13:29:32 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:29:32 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:29:32 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:29:32 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:29:32 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:29:32 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:29:32 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:29:32 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:29:32 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:29:32 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:29:32 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:29:34 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:29:34 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:29:34 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:29:37 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:29:37 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:29:37 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:29:37 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:29:37 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:29:37 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:29:37 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:29:37 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:29:37 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:29:37 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:29:37 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:29:37 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:29:37 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:29:37 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:29:40 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:29:40 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:29:40 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:29:40 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:29:40 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:29:40 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:29:40 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:29:40 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:29:40 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:29:40 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:29:40 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:29:40 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:29:40 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:29:40 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:29:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:29:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:29:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:29:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:29:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:29:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:29:43 | INFO     | VALIDATION | DRAFT:   Retrieved 6 RFP context chunks
2025-08-05 13:29:43 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:29:43 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:29:43 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:29:43 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:29:43 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:29:43 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:29:43 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:29:43 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:29:43 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:29:43 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 13:29:43 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:29:43 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 13:29:43 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 13:29:43 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:29:45 | INFO     | VALIDATION | DRAFT:   Retrieved 1 client context chunks
2025-08-05 13:29:45 | INFO     | VALIDATION | DRAFT:   LLM attempt 1/3 for Quality Control and Performance Monitoring
2025-08-05 13:30:23 | INFO     | VALIDATION | DRAFT:   Processing technical section content
2025-08-05 13:30:23 | INFO     | VALIDATION | DRAFT:   Technical section processed, length: 5129 chars
2025-08-05 13:30:23 | INFO     | VALIDATION | DRAFT:   Successfully generated draft for Quality Control and Performance Monitoring (5129 chars)
2025-08-05 13:30:23 | INFO     | VALIDATION | DRAFT: Successfully processed section 3/5
2025-08-05 13:30:23 | INFO     | VALIDATION | DRAFT: Processing main section 4/5: Tab D - Factor 3 - Technical Approach
2025-08-05 13:30:23 | INFO     | VALIDATION | DRAFT: Processing section 4.0 - Tab D - Factor 3 - Technical Approach
2025-08-05 13:30:23 | INFO     | VALIDATION | DRAFT: Cover letter detected: False
2025-08-05 13:30:23 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:30:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:30:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:30:23 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:30:23 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:30:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:30:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:30:23 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:30:23 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:30:23 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:30:23 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:30:24 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:30:24 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:30:24 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:30:26 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:30:26 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:30:26 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:30:26 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:30:26 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:30:26 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:30:26 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:30:26 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:30:26 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:30:26 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:30:26 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:30:27 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:30:27 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:30:27 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:30:29 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:30:29 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:30:29 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:30:29 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:30:29 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:30:29 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:30:29 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:30:29 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:30:29 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:30:29 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:30:29 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:30:29 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:30:29 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:30:29 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:30:32 | INFO     | VALIDATION | DRAFT: Retrieved 6 RFP context chunks
2025-08-05 13:30:32 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:30:32 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:30:32 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:30:32 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:30:32 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:30:32 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:30:32 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:30:32 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:30:32 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:30:32 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 13:30:32 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:30:32 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 13:30:32 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 13:30:32 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:30:35 | INFO     | VALIDATION | DRAFT: Retrieved 1 client context chunks
2025-08-05 13:30:35 | INFO     | VALIDATION | DRAFT: LLM attempt 1/3 for Tab D - Factor 3 - Technical Approach
2025-08-05 13:30:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:30:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:30:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:30:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:30:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:30:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:31:35 | INFO     | VALIDATION | DRAFT: Processing technical section content
2025-08-05 13:31:35 | INFO     | VALIDATION | DRAFT: Technical section processed, length: 6040 chars
2025-08-05 13:31:35 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab D - Factor 3 - Technical Approach: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:31:35 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 1: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:31:35 | INFO     | VALIDATION | DRAFT: LLM attempt 2/3 for Tab D - Factor 3 - Technical Approach
2025-08-05 13:31:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:31:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:31:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:31:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:31:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:31:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:32:18 | INFO     | VALIDATION | DRAFT: Processing technical section content
2025-08-05 13:32:18 | INFO     | VALIDATION | DRAFT: Technical section processed, length: 6040 chars
2025-08-05 13:32:18 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab D - Factor 3 - Technical Approach: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:32:18 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 2: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:32:18 | INFO     | VALIDATION | DRAFT: LLM attempt 3/3 for Tab D - Factor 3 - Technical Approach
2025-08-05 13:32:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:32:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:32:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:32:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:32:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:32:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:33:11 | INFO     | VALIDATION | DRAFT: Processing technical section content
2025-08-05 13:33:11 | INFO     | VALIDATION | DRAFT: Technical section processed, length: 6040 chars
2025-08-05 13:33:11 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab D - Factor 3 - Technical Approach: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:33:11 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 3: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:33:11 | WARNING  | VALIDATION | DRAFT: Using draft with validation warnings
2025-08-05 13:33:11 | INFO     | VALIDATION | DRAFT: Successfully generated draft for Tab D - Factor 3 - Technical Approach (6040 chars)
2025-08-05 13:33:11 | INFO     | VALIDATION | DRAFT: Processing 6 subsections
2025-08-05 13:33:11 | INFO     | VALIDATION | DRAFT:   Processing section 4.1 - TASK 1 – Program Management and Administration
2025-08-05 13:33:11 | INFO     | VALIDATION | DRAFT:   Cover letter detected: False
2025-08-05 13:33:11 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:33:11 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:33:11 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:33:11 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:33:11 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:33:11 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:33:11 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:33:11 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:33:11 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:33:11 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:33:11 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:33:14 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:33:14 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:33:14 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:33:29 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:33:29 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:33:29 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:33:29 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:33:29 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:33:29 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:33:29 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:33:29 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:33:29 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:33:29 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:33:29 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:33:30 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:33:30 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:33:30 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:33:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:33:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:33:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:33:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:33:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:33:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:33:42 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:33:42 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:33:42 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:33:42 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:33:42 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:33:42 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:33:42 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:33:42 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:33:42 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:33:42 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:33:42 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:33:42 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:33:42 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:33:42 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:33:50 | INFO     | VALIDATION | DRAFT:   Retrieved 6 RFP context chunks
2025-08-05 13:33:50 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:33:50 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:33:50 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:33:50 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:33:50 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:33:50 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:33:50 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:33:50 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:33:50 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:33:50 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 13:33:50 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:33:50 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 13:33:50 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 13:33:50 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:34:01 | INFO     | VALIDATION | DRAFT:   Retrieved 1 client context chunks
2025-08-05 13:34:01 | INFO     | VALIDATION | DRAFT:   LLM attempt 1/3 for TASK 1 – Program Management and Administration
2025-08-05 13:34:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:34:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:34:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:34:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:34:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:34:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:35:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:35:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:35:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:35:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:35:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:35:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:36:02 | INFO     | VALIDATION | DRAFT:   Processing technical section content
2025-08-05 13:36:02 | INFO     | VALIDATION | DRAFT:   Technical section processed, length: 5075 chars
2025-08-05 13:36:02 | INFO     | VALIDATION | DRAFT:   Successfully generated draft for TASK 1 – Program Management and Administration (5075 chars)
2025-08-05 13:36:02 | INFO     | VALIDATION | DRAFT:   Processing section 4.2 - TASK 2 – Information Management
2025-08-05 13:36:02 | INFO     | VALIDATION | DRAFT:   Cover letter detected: False
2025-08-05 13:36:02 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:36:02 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:36:02 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:36:02 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:36:02 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:36:02 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:36:02 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:36:02 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:36:02 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:36:02 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:36:02 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:36:21 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:36:21 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:36:21 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:36:34 | WARNING  | VALIDATION | DRAFT:   ChromaDB timeout for query: Requirements and specifications for TASK 2 – Infor...
2025-08-05 13:36:34 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:36:34 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:36:34 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:36:34 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:36:34 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:36:34 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:36:34 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:36:34 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:36:34 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:36:34 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:36:34 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:36:34 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:36:34 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:36:34 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:36:37 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:36:37 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:36:37 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:36:37 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:36:37 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:36:37 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:36:37 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:36:37 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:36:37 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:36:37 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:36:37 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:36:37 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:36:37 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:36:37 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:36:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:36:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:36:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:36:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:36:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:36:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:36:42 | INFO     | VALIDATION | DRAFT:   Retrieved 4 RFP context chunks
2025-08-05 13:36:42 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:36:42 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:36:42 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:36:42 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:36:42 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:36:42 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:36:42 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:36:42 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:36:42 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:36:42 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 13:36:42 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:36:42 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 13:36:42 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 13:36:42 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:36:45 | INFO     | VALIDATION | DRAFT:   Retrieved 1 client context chunks
2025-08-05 13:36:45 | INFO     | VALIDATION | DRAFT:   LLM attempt 1/3 for TASK 2 – Information Management
2025-08-05 13:37:21 | INFO     | VALIDATION | DRAFT:   Processing technical section content
2025-08-05 13:37:21 | INFO     | VALIDATION | DRAFT:   Technical section processed, length: 4813 chars
2025-08-05 13:37:21 | INFO     | VALIDATION | DRAFT:   Successfully generated draft for TASK 2 – Information Management (4813 chars)
2025-08-05 13:37:21 | INFO     | VALIDATION | DRAFT:   Processing section 4.3 - TASK 3 – Program Compliance
2025-08-05 13:37:21 | INFO     | VALIDATION | DRAFT:   Cover letter detected: False
2025-08-05 13:37:21 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:37:21 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:37:21 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:37:21 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:37:21 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:37:21 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:37:21 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:37:21 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:37:21 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:37:21 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:37:21 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:37:22 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:37:22 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:37:22 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:37:24 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:37:24 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:37:24 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:37:24 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:37:24 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:37:24 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:37:24 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:37:24 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:37:24 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:37:24 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:37:24 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:37:25 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:37:25 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:37:25 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:37:27 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:37:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:37:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:37:27 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:37:27 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:37:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:37:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:37:27 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:37:27 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:37:27 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:37:27 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:37:27 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:37:27 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:37:27 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:37:29 | INFO     | VALIDATION | DRAFT:   Retrieved 6 RFP context chunks
2025-08-05 13:37:29 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:37:29 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:37:29 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:37:29 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:37:29 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:37:29 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:37:29 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:37:29 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:37:29 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:37:29 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 13:37:29 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:37:29 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 13:37:29 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 13:37:29 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:37:31 | INFO     | VALIDATION | DRAFT:   Retrieved 1 client context chunks
2025-08-05 13:37:31 | INFO     | VALIDATION | DRAFT:   LLM attempt 1/3 for TASK 3 – Program Compliance
2025-08-05 13:37:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:37:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:37:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:37:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:37:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:37:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:38:18 | INFO     | VALIDATION | DRAFT:   Processing technical section content
2025-08-05 13:38:18 | INFO     | VALIDATION | DRAFT:   Technical section processed, length: 5341 chars
2025-08-05 13:38:18 | INFO     | VALIDATION | DRAFT:   Successfully generated draft for TASK 3 – Program Compliance (5341 chars)
2025-08-05 13:38:18 | INFO     | VALIDATION | DRAFT:   Processing section 4.4 - TASK 4 – Training and Outreach
2025-08-05 13:38:18 | INFO     | VALIDATION | DRAFT:   Cover letter detected: False
2025-08-05 13:38:18 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:38:18 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:38:18 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:38:18 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:38:18 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:38:18 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:38:18 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:38:18 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:38:18 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:38:18 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:38:18 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:38:20 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:38:20 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:38:20 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:38:22 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:38:22 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:38:22 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:38:22 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:38:22 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:38:22 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:38:22 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:38:22 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:38:22 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:38:22 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:38:22 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:38:22 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:38:22 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:38:22 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:38:24 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:38:24 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:38:24 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:38:24 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:38:24 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:38:24 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:38:24 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:38:24 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:38:24 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:38:24 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:38:24 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:38:24 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:38:24 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:38:24 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:38:26 | INFO     | VALIDATION | DRAFT:   Retrieved 6 RFP context chunks
2025-08-05 13:38:26 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:38:26 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:38:26 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:38:26 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:38:26 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:38:26 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:38:26 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:38:26 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:38:26 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:38:26 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 13:38:26 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:38:27 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 13:38:27 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 13:38:27 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:38:29 | INFO     | VALIDATION | DRAFT:   Retrieved 1 client context chunks
2025-08-05 13:38:29 | INFO     | VALIDATION | DRAFT:   LLM attempt 1/3 for TASK 4 – Training and Outreach
2025-08-05 13:38:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:38:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:38:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:38:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:38:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:38:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:39:24 | INFO     | VALIDATION | DRAFT:   Processing technical section content
2025-08-05 13:39:24 | INFO     | VALIDATION | DRAFT:   Technical section processed, length: 4894 chars
2025-08-05 13:39:24 | INFO     | VALIDATION | DRAFT:   Successfully generated draft for TASK 4 – Training and Outreach (4894 chars)
2025-08-05 13:39:24 | INFO     | VALIDATION | DRAFT:   Processing section 4.5 - TASK 5 – Regulatory Support
2025-08-05 13:39:24 | INFO     | VALIDATION | DRAFT:   Cover letter detected: False
2025-08-05 13:39:24 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:39:24 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:39:24 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:39:24 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:39:24 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:39:24 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:39:24 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:39:24 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:39:24 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:39:24 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:39:24 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:39:26 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:39:26 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:39:26 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:39:28 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:39:28 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:39:28 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:39:28 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:39:28 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:39:28 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:39:28 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:39:28 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:39:28 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:39:28 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:39:28 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:39:28 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:39:28 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:39:28 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:39:30 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:39:30 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:39:30 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:39:30 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:39:30 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:39:30 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:39:30 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:39:30 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:39:30 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:39:30 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:39:30 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:39:30 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:39:30 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:39:30 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:39:32 | INFO     | VALIDATION | DRAFT:   Retrieved 6 RFP context chunks
2025-08-05 13:39:32 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:39:32 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:39:32 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:39:32 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:39:32 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:39:32 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:39:32 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:39:32 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:39:32 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:39:32 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 13:39:32 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:39:33 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 13:39:33 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 13:39:33 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:39:35 | INFO     | VALIDATION | DRAFT:   Retrieved 1 client context chunks
2025-08-05 13:39:35 | INFO     | VALIDATION | DRAFT:   LLM attempt 1/3 for TASK 5 – Regulatory Support
2025-08-05 13:39:40 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:39:40 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:39:40 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:39:40 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:39:40 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:39:40 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:39:45 | INFO     | VALIDATION | Stopping all schedulers...
2025-08-05 13:39:45 | INFO     | VALIDATION | Proposal scheduler stopped
2025-08-05 13:39:45 | INFO     | VALIDATION | Custom opps scheduler stopped
2025-08-05 13:39:45 | INFO     | VALIDATION | SAM opps scheduler stopped
2025-08-05 13:39:45 | INFO     | VALIDATION | Client process queue scheduler stopped
2025-08-05 13:39:45 | INFO     | VALIDATION | Datametastore queue scheduler stopped
2025-08-05 13:39:45 | INFO     | VALIDATION | Simulation scheduler stopped
2025-08-05 13:39:45 | INFO     | VALIDATION | All schedulers stopped
2025-08-05 13:39:45 | INFO     | VALIDATION | Application shutdown completed
2025-08-05 13:39:57 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-05 13:39:58 | INFO     | VALIDATION | ProposalCriticismSchedulerService initialized
2025-08-05 13:40:19 | INFO     | VALIDATION | DRAFT:   Processing technical section content
2025-08-05 13:40:19 | INFO     | VALIDATION | DRAFT:   Technical section processed, length: 5614 chars
2025-08-05 13:40:19 | INFO     | VALIDATION | DRAFT:   Successfully generated draft for TASK 5 – Regulatory Support (5614 chars)
2025-08-05 13:40:19 | INFO     | VALIDATION | DRAFT:   Processing section 4.6 - TASK 6 – Optional – Surge
2025-08-05 13:40:19 | INFO     | VALIDATION | DRAFT:   Cover letter detected: False
2025-08-05 13:40:19 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:40:19 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:40:19 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:40:19 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:40:19 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:40:19 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:40:19 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:40:19 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:40:19 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:40:19 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:40:19 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:40:21 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:40:21 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:40:21 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:40:23 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:40:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:40:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:40:23 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:40:23 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:40:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:40:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:40:23 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:40:23 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:40:23 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:40:23 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:40:23 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:40:23 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:40:23 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:40:25 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:40:25 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:40:25 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:40:25 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:40:25 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:40:25 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:40:25 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:40:25 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:40:25 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:40:25 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:40:25 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:40:25 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:40:25 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:40:25 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:40:27 | INFO     | VALIDATION | DRAFT:   Retrieved 6 RFP context chunks
2025-08-05 13:40:27 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:40:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:40:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:40:27 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:40:27 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:40:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:40:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:40:27 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:40:27 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:40:27 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 13:40:27 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:40:27 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 13:40:27 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 13:40:27 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:40:29 | INFO     | VALIDATION | Database initialized successfully
2025-08-05 13:40:29 | INFO     | VALIDATION | All schedulers started successfully
2025-08-05 13:40:29 | INFO     | VALIDATION | Schedulers are disabled on startup (via env variable)
2025-08-05 13:40:29 | INFO     | VALIDATION | DRAFT:   Retrieved 1 client context chunks
2025-08-05 13:40:29 | INFO     | VALIDATION | DRAFT:   LLM attempt 1/3 for TASK 6 – Optional – Surge
2025-08-05 13:40:50 | INFO     | VALIDATION | Application shutdown completed
2025-08-05 13:41:07 | INFO     | VALIDATION | DRAFT:   Processing technical section content
2025-08-05 13:41:07 | INFO     | VALIDATION | DRAFT:   Technical section processed, length: 5046 chars
2025-08-05 13:41:07 | INFO     | VALIDATION | DRAFT:   Successfully generated draft for TASK 6 – Optional – Surge (5046 chars)
2025-08-05 13:41:07 | INFO     | VALIDATION | DRAFT: Successfully processed section 4/5
2025-08-05 13:41:07 | INFO     | VALIDATION | DRAFT: Processing main section 5/5: Tab E - Factor 4 - Demonstrated Corporate Experience
2025-08-05 13:41:07 | INFO     | VALIDATION | DRAFT: Processing section 5.0 - Tab E - Factor 4 - Demonstrated Corporate Experience
2025-08-05 13:41:07 | INFO     | VALIDATION | DRAFT: Cover letter detected: False
2025-08-05 13:41:07 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:41:07 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:41:07 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:41:07 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:41:07 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:41:07 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:41:07 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:41:07 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:41:07 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:41:07 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:41:07 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:41:08 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:41:08 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:41:08 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:41:10 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:41:10 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:41:10 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:41:10 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:41:10 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:41:10 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:41:10 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:41:10 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:41:10 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:41:10 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:41:10 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:41:10 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:41:10 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:41:10 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:41:12 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:41:12 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:41:12 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:41:12 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:41:12 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:41:12 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:41:12 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:41:12 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:41:12 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:41:12 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:41:12 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:41:12 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:41:12 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:41:12 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:41:15 | INFO     | VALIDATION | DRAFT: Retrieved 6 RFP context chunks
2025-08-05 13:41:15 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:41:15 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:41:15 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:41:15 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:41:15 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:41:15 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:41:15 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:41:15 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:41:15 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:41:15 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 13:41:15 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:41:15 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 13:41:15 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 13:41:15 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:41:18 | INFO     | VALIDATION | DRAFT: Retrieved 1 client context chunks
2025-08-05 13:41:18 | INFO     | VALIDATION | DRAFT: LLM attempt 1/3 for Tab E - Factor 4 - Demonstrated Corporate Experience
2025-08-05 13:41:42 | INFO     | VALIDATION | DRAFT: Processing technical section content
2025-08-05 13:41:42 | INFO     | VALIDATION | DRAFT: Technical section processed, length: 4506 chars
2025-08-05 13:41:42 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab E - Factor 4 - Demonstrated Corporate Experience: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:41:42 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 1: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:41:42 | INFO     | VALIDATION | DRAFT: LLM attempt 2/3 for Tab E - Factor 4 - Demonstrated Corporate Experience
2025-08-05 13:41:52 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-05 13:41:52 | INFO     | VALIDATION | ProposalCriticismSchedulerService initialized
2025-08-05 13:42:23 | INFO     | VALIDATION | Database initialized successfully
2025-08-05 13:42:23 | INFO     | VALIDATION | Starting all schedulers...
2025-08-05 13:42:23 | INFO     | VALIDATION | Proposal scheduler started with 60 second interval
2025-08-05 13:42:23 | INFO     | VALIDATION | Proposal criticism scheduler started successfully
2025-08-05 13:42:23 | INFO     | VALIDATION | Custom opps scheduler started with 60 second interval
2025-08-05 13:42:23 | INFO     | VALIDATION | SAM opps scheduler started with 60 second interval
2025-08-05 13:42:23 | INFO     | VALIDATION | Client process queue scheduler started with 60 second interval
2025-08-05 13:42:23 | INFO     | VALIDATION | Datametastore queue scheduler started with 60 second interval
2025-08-05 13:42:23 | INFO     | VALIDATION | Simulation scheduler started with 60 second interval
2025-08-05 13:42:23 | INFO     | VALIDATION | All schedulers started
2025-08-05 13:42:23 | INFO     | VALIDATION | All schedulers started successfully
2025-08-05 13:42:23 | INFO     | VALIDATION | Schedulers are disabled on startup (via env variable)
2025-08-05 13:42:26 | INFO     | VALIDATION | DRAFT: Processing technical section content
2025-08-05 13:42:26 | INFO     | VALIDATION | DRAFT: Technical section processed, length: 4506 chars
2025-08-05 13:42:26 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab E - Factor 4 - Demonstrated Corporate Experience: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:42:26 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 2: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:42:26 | INFO     | VALIDATION | DRAFT: LLM attempt 3/3 for Tab E - Factor 4 - Demonstrated Corporate Experience
2025-08-05 13:43:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:43:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:43:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:43:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:43:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:43:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:43:26 | INFO     | VALIDATION | DRAFT: Processing technical section content
2025-08-05 13:43:26 | INFO     | VALIDATION | DRAFT: Technical section processed, length: 4506 chars
2025-08-05 13:43:26 | WARNING  | VALIDATION | DRAFT: Quality validation failed for Tab E - Factor 4 - Demonstrated Corporate Experience: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:43:26 | WARNING  | VALIDATION | DRAFT: Validation errors on attempt 3: ['Content may not be sufficiently relevant to section title']
2025-08-05 13:43:26 | WARNING  | VALIDATION | DRAFT: Using draft with validation warnings
2025-08-05 13:43:26 | INFO     | VALIDATION | DRAFT: Successfully generated draft for Tab E - Factor 4 - Demonstrated Corporate Experience (4506 chars)
2025-08-05 13:43:26 | INFO     | VALIDATION | DRAFT: Processing 3 subsections
2025-08-05 13:43:26 | INFO     | VALIDATION | DRAFT:   Processing section 5.1 - Experience Example 1
2025-08-05 13:43:26 | INFO     | VALIDATION | DRAFT:   Cover letter detected: False
2025-08-05 13:43:26 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:43:26 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:43:26 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:43:26 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:43:26 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:43:26 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:43:26 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:43:26 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:43:26 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:43:26 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:43:26 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:43:28 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:43:28 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:43:28 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:43:30 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:43:30 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:43:30 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:43:30 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:43:30 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:43:30 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:43:30 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:43:30 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:43:30 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:43:30 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:43:30 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:43:31 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:43:31 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:43:31 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:43:32 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:43:32 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:43:32 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:43:32 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:43:32 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:43:32 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:43:32 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:43:32 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:43:32 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:43:32 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:43:32 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:43:33 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:43:33 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:43:33 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:43:35 | INFO     | VALIDATION | DRAFT:   Retrieved 6 RFP context chunks
2025-08-05 13:43:35 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:43:35 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:43:35 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:43:35 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:43:35 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:43:35 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:43:35 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:43:35 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:43:35 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:43:35 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 13:43:35 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:43:35 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 13:43:35 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 13:43:35 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:43:37 | INFO     | VALIDATION | DRAFT:   Retrieved 1 client context chunks
2025-08-05 13:43:37 | INFO     | VALIDATION | DRAFT:   LLM attempt 1/3 for Experience Example 1
2025-08-05 13:44:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:44:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:44:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:44:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:44:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:44:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:44:30 | INFO     | VALIDATION | DRAFT:   Processing technical section content
2025-08-05 13:44:30 | INFO     | VALIDATION | DRAFT:   Technical section processed, length: 4007 chars
2025-08-05 13:44:30 | INFO     | VALIDATION | DRAFT:   Successfully generated draft for Experience Example 1 (4007 chars)
2025-08-05 13:44:30 | INFO     | VALIDATION | DRAFT:   Processing section 5.2 - Experience Example 2
2025-08-05 13:44:30 | INFO     | VALIDATION | DRAFT:   Cover letter detected: False
2025-08-05 13:44:30 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:44:30 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:44:30 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:44:30 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:44:30 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:44:30 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:44:30 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:44:30 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:44:30 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:44:30 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:44:30 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:44:31 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:44:31 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:44:31 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:44:33 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:44:33 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:44:33 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:44:33 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:44:33 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:44:33 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:44:33 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:44:33 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:44:33 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:44:33 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:44:33 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:44:34 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:44:34 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:44:34 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:44:36 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:44:36 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:44:36 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:44:36 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:44:36 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:44:36 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:44:36 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:44:36 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:44:36 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:44:36 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:44:36 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:44:36 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:44:36 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:44:36 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:44:39 | INFO     | VALIDATION | DRAFT:   Retrieved 6 RFP context chunks
2025-08-05 13:44:39 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:44:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:44:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:44:39 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:44:39 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:44:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:44:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:44:39 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:44:39 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:44:39 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 13:44:39 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:44:39 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 13:44:39 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 13:44:39 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:44:41 | INFO     | VALIDATION | DRAFT:   Retrieved 1 client context chunks
2025-08-05 13:44:41 | INFO     | VALIDATION | DRAFT:   LLM attempt 1/3 for Experience Example 2
2025-08-05 13:45:14 | INFO     | VALIDATION | DRAFT:   Processing technical section content
2025-08-05 13:45:14 | INFO     | VALIDATION | DRAFT:   Technical section processed, length: 4358 chars
2025-08-05 13:45:14 | INFO     | VALIDATION | DRAFT:   Successfully generated draft for Experience Example 2 (4358 chars)
2025-08-05 13:45:14 | INFO     | VALIDATION | DRAFT:   Processing section 5.3 - Experience Example 3
2025-08-05 13:45:14 | INFO     | VALIDATION | DRAFT:   Cover letter detected: False
2025-08-05 13:45:14 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:45:14 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:45:14 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:45:14 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:45:14 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:45:14 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:45:14 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:45:14 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:45:14 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:45:14 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:45:14 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:45:16 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:45:16 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:45:16 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:45:19 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:45:19 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:45:19 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:45:19 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:45:19 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:45:19 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:45:19 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:45:19 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:45:19 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:45:19 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:45:19 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:45:19 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:45:19 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:45:19 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:45:21 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:45:21 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:45:21 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:45:21 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:45:21 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:45:21 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:45:21 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-05 13:45:21 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:45:21 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-05 13:45:21 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-05 13:45:21 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:45:21 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-05 13:45:21 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-05 13:45:21 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-05 13:45:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:45:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:45:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:45:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:45:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:45:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:45:24 | INFO     | VALIDATION | DRAFT:   Retrieved 6 RFP context chunks
2025-08-05 13:45:24 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:45:24 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:45:24 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:45:24 | DEBUG    | VALIDATION | Returning second part result: adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:45:24 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:45:24 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:45:24 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'adeptengineeringsolutions'] (type: <class 'list'>)
2025-08-05 13:45:24 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-05 13:45:24 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions (type: <class 'str'>)
2025-08-05 13:45:24 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions'] (length: 1)
2025-08-05 13:45:24 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:45:24 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: 0b96be05f49d12f8a3590a93c1d52c6a and version: 8
2025-08-05 13:45:24 | INFO     | VALIDATION | Constructed versioned_collection_name: 0b96be05f49d12f8a3590a93c1d52c6a.8, version_number: 8
2025-08-05 13:45:24 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9004 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_adeptengineeringsolutions
2025-08-05 13:45:26 | INFO     | VALIDATION | DRAFT:   Retrieved 1 client context chunks
2025-08-05 13:45:26 | INFO     | VALIDATION | DRAFT:   LLM attempt 1/3 for Experience Example 3
2025-08-05 13:45:59 | INFO     | VALIDATION | DRAFT:   Processing technical section content
2025-08-05 13:45:59 | INFO     | VALIDATION | DRAFT:   Technical section processed, length: 4641 chars
2025-08-05 13:45:59 | INFO     | VALIDATION | DRAFT:   Successfully generated draft for Experience Example 3 (4641 chars)
2025-08-05 13:45:59 | INFO     | VALIDATION | DRAFT: Successfully processed section 5/5
2025-08-05 13:45:59 | INFO     | VALIDATION | DRAFT: Generation complete - 5/5 sections successful
2025-08-05 13:45:59 | INFO     | VALIDATION | DRAFT: Cover letters generated: 1
2025-08-05 13:46:15 | INFO     | VALIDATION | Updated CustomOppsTable record with opportunity_id=iRiYNgd8RC
2025-08-05 13:46:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:46:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:46:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:46:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:46:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:46:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:47:16 | INFO     | VALIDATION | Processing cover page with ID: 4195
2025-08-05 13:47:20 | INFO     | VALIDATION | Found cover page: cover-page-1751947116383.png
2025-08-05 13:47:20 | INFO     | VALIDATION | Using compliance settings for cover page: font=Times-Roman, body_size=12, header_size=14
2025-08-05 13:47:20 | INFO     | VALIDATION | Processing cover page with content type: image/png
2025-08-05 13:47:20 | INFO     | VALIDATION | Original image size: 1600 x 2000
2025-08-05 13:47:20 | INFO     | VALIDATION | Resized image to print quality: 2550 x 3300 pixels
2025-08-05 13:47:20 | INFO     | VALIDATION | Created image element to cover entire page with no text
2025-08-05 13:47:20 | INFO     | VALIDATION | Successfully created image cover page from: cover-page-1751947116383.png
2025-08-05 13:47:20 | INFO     | VALIDATION | Image-only cover page prepared: 1 elements
2025-08-05 13:47:20 | INFO     | VALIDATION | Converting draft with 5 sections
2025-08-05 13:47:20 | INFO     | VALIDATION | Processing section 1: <class 'dict'>
2025-08-05 13:47:20 | INFO     | VALIDATION | Section title: 1.0 Tab A - Proposal Cover/Transmittal Letter
2025-08-05 13:47:20 | INFO     | VALIDATION | Section content length: 1943 characters
2025-08-05 13:47:20 | INFO     | VALIDATION | Content preview: Adept Engineering Solutions
7130 Minstrel Way, Suite 210
Columbia, Maryland 21045
adeptengr.com

August 05, 2025

[Insert Government Agency Address Here - Not Provided in Context]

***Subject: Proposa...
2025-08-05 13:47:20 | INFO     | VALIDATION | Processing section 2: <class 'dict'>
2025-08-05 13:47:20 | INFO     | VALIDATION | Section title: 2.0 Tab B - Factor 1 - Staffing & Key Personnel Qualifications
2025-08-05 13:47:20 | INFO     | VALIDATION | Section content length: 5487 characters
2025-08-05 13:47:20 | INFO     | VALIDATION | Content preview: Adept Engineering Solutions will assemble a highly qualified team dedicated to consistently delivering exceptional support for the Export Control Group (ECG). Our approach prioritizes proactive recrui...
2025-08-05 13:47:20 | INFO     | VALIDATION | Processing 4 subsections
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 1 title: 2.1 Recruitment, Hiring, and Retention Approach
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 1 content length: 5538 characters
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 2 title: 2.2 Certifications and Training Processes
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 2 content length: 4614 characters
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 3 title: 2.3 Resume of Proposed Key Personnel
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 3 content length: 6714 characters
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 4 title: 2.4 Tentative/Contingent Offer Letter
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 4 content length: 1943 characters
2025-08-05 13:47:20 | INFO     | VALIDATION | Processing section 3: <class 'dict'>
2025-08-05 13:47:20 | INFO     | VALIDATION | Section title: 3.0 Tab C - Factor 2 - Management Approach
2025-08-05 13:47:20 | INFO     | VALIDATION | Section content length: 4501 characters
2025-08-05 13:47:20 | INFO     | VALIDATION | Content preview: ***Employee Turnover & Vacancy Mitigation****

Adept Engineering Solutions maintains a consistently low employee turnover rate of 8% across similar federal contracts over the past three years. Average...
2025-08-05 13:47:20 | INFO     | VALIDATION | Processing 3 subsections
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 1 title: 3.1 Employee Turnover and Solutions
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 1 content length: 4706 characters
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 2 title: 3.2 Surge Support Availability
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 2 content length: 4407 characters
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 3 title: 3.3 Quality Control and Performance Monitoring
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 3 content length: 5129 characters
2025-08-05 13:47:20 | INFO     | VALIDATION | Processing section 4: <class 'dict'>
2025-08-05 13:47:20 | INFO     | VALIDATION | Section title: 4.0 Tab D - Factor 3 - Technical Approach
2025-08-05 13:47:20 | INFO     | VALIDATION | Section content length: 6040 characters
2025-08-05 13:47:20 | INFO     | VALIDATION | Content preview: Adept Engineering Solutions understands the critical importance of a robust Export Control Group (ECG) in ensuring compliance with evolving U.S. Export Control regulations (EAR, ITAR, and OFAC). Our a...
2025-08-05 13:47:20 | INFO     | VALIDATION | Processing 6 subsections
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 1 title: 4.1 TASK 1 – Program Management and Administration
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 1 content length: 5075 characters
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 2 title: 4.2 TASK 2 – Information Management
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 2 content length: 4813 characters
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 3 title: 4.3 TASK 3 – Program Compliance
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 3 content length: 5341 characters
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 4 title: 4.4 TASK 4 – Training and Outreach
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 4 content length: 4894 characters
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 5 title: 4.5 TASK 5 – Regulatory Support
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 5 content length: 5614 characters
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 6 title: 4.6 TASK 6 – Optional – Surge
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 6 content length: 5046 characters
2025-08-05 13:47:20 | INFO     | VALIDATION | Processing section 5: <class 'dict'>
2025-08-05 13:47:20 | INFO     | VALIDATION | Section title: 5.0 Tab E - Factor 4 - Demonstrated Corporate Experience
2025-08-05 13:47:20 | INFO     | VALIDATION | Section content length: 4506 characters
2025-08-05 13:47:20 | INFO     | VALIDATION | Content preview: Adept Engineering Solutions delivers consistently high-quality export control and compliance support to federal agencies. The following examples demonstrate our proven ability to perform the tasks out...
2025-08-05 13:47:20 | INFO     | VALIDATION | Processing 3 subsections
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 1 title: 5.1 Experience Example 1
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 1 content length: 4007 characters
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 2 title: 5.2 Experience Example 2
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 2 content length: 4358 characters
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 3 title: 5.3 Experience Example 3
2025-08-05 13:47:20 | INFO     | VALIDATION | Subsection 3 content length: 4641 characters
2025-08-05 13:47:20 | INFO     | VALIDATION | Generated markdown with 100258 characters
2025-08-05 13:47:20 | INFO     | VALIDATION | Checking for TOC: opportunity_details=True, has_toc_text=True
2025-08-05 13:47:20 | INFO     | VALIDATION | TOC text found: [{"title": "Tab A - Proposal Cover/Transmittal Letter", "description": "Standard cover letter for th...
2025-08-05 13:47:20 | INFO     | VALIDATION | TOC JSON parsed successfully, 5 items
2025-08-05 13:47:20 | INFO     | VALIDATION | Content generated (length: 100258)
2025-08-05 13:47:20 | INFO     | VALIDATION | Using compliance formatting: margin=50pts, font=Times-Roman, body_size=12, header_size=14, footer_size=10, line_spacing=1.5
2025-08-05 13:47:20 | INFO     | VALIDATION | Processing cover page with 1 elements
2025-08-05 13:47:20 | INFO     | VALIDATION | Created templates for cover page and regular pages
2025-08-05 13:47:20 | INFO     | VALIDATION | Adding 1 cover page elements to PDF
2025-08-05 13:47:20 | INFO     | VALIDATION | Cover page added
2025-08-05 13:47:20 | INFO     | VALIDATION | Generating TOC with accurate page numbers
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Processing 906 lines of markdown content
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['KPI', 'Target', 'Measurement Frequency']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Time to Fill', '< 60 days', 'Monthly']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Quality of Hire', '80% positive performance reviews', 'Annually']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Employee Retention Rate', '> 90%', 'Annually']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Employee Satisfaction Score', '> 4.0 (out of 5)', 'Bi-Annually']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Certification', 'Role Applicability', 'Renewal Frequency', 'Verification Method']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['CompTIA Security+', 'All personnel with system access', 'Every 3 years', 'Digital Badge/Record']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Certified Information Systems Security Professional (CISSP)', 'Security Engineers, Architects, Managers', 'Every 3 years', 'Digital Badge/Record']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Project Management Professional (PMP)', 'Project Managers, Team Leads', 'Every 3 years', 'Digital Badge/Record']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Certified ScrumMaster (CSM)', 'Agile Team Members', 'Every 2 years', 'Digital Badge/Record']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['DHS Anti-Terrorism Awareness Training', 'All personnel accessing DHS facilities/systems', 'Annually', 'Completion Record']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Ending table with 6 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table with 6 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Processing 6 cleaned table rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Successfully added table with 6 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Splitting long line (1007 characters)
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['KPI', 'Target', 'Measurement Frequency', 'Reporting Frequency']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Compliance Review Accuracy', '98%', 'Monthly', 'Monthly']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Deliverable On-Time Rate', '100%', 'Monthly', 'Monthly']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Data Validation Error Rate', '<1%', 'Monthly', 'Monthly']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Customer Satisfaction', '4.5/5 stars', 'Quarterly', 'Quarterly']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['KPI', 'Target', 'Measurement Frequency', 'Reporting Frequency']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Employee Turnover Rate', '< 10%', 'Monthly', 'Quarterly']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Time-to-Fill (Critical)', '< 30 days', 'Monthly', 'Quarterly']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Employee Satisfaction', '> 80%', 'Annually', 'Annually']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Training Hours/Employee', '> 40 hrs', 'Annually', 'Annually']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['KPI', 'Target', 'Measurement Frequency', 'Reporting Frequency']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Surge Support Activation Rate', '< 5%', 'Monthly', 'Quarterly']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Time to Deploy Surge Resource', '< 24 hours', 'Monthly', 'Quarterly']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Customer Satisfaction (Surge Support)', '> 90%', 'Post-Deployment', 'Quarterly']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Impact to Deliverables (Due to Surge)', '< 5%', 'Monthly', 'Quarterly']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['KPI', 'Target', 'Measurement Method', 'Reporting Frequency']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Deliverable Acceptance Rate', '≥ 98%', 'Number of deliverables accepted without revision / Total deliverables submitted', 'Bi-Annual']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Revision Cycle Time', '≤ 2 business days', 'Time elapsed between deliverable submission and final government acceptance', 'Bi-Annual']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Number of Critical Errors', '≤ 1 per quarter', 'Identification of errors requiring significant rework or impacting compliance', 'Bi-Annual']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Adherence to Style Guide', '≥ 95%', 'Percentage of deliverables conforming to the established style guide', 'Bi-Annual']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Tool', 'Purpose', 'Key Features']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Automated Screening Software', 'Restricted Party Screening', 'Real-time screening against multiple restricted party lists, audit trails']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['EDMS', 'Recordkeeping & Document Management', 'Secure storage, version control, audit trails, access control']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['ECCN Classification Tool', 'Commodity Jurisdiction', 'Database of ECCNs, classification guidance, automated lookup']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Ending table with 4 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table with 4 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Processing 4 cleaned table rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Successfully added table with 4 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['****Quality Control Activity****', '****Description****', '****Frequency****', '****Responsible Party****', '****Deliverable/Metric****']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['****Daily Task Review****', 'Each team member will review their completed tasks to ensure accuracy and completeness.', 'Daily', 'Individual Team Member', '100% task completion with no errors']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['****Peer Review****', 'A designated peer will review a sample of completed tasks to identify potential issues and provide constructive feedback.', 'Weekly', 'Senior Analyst', 'Error rate < 2%']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['****Supervisor Review****', 'The Senior/Lead Analyst will review a sample of completed tasks to ensure compliance with all applicable regulations and procedures.', 'Bi-Weekly', 'Senior/Lead Analyst', '100% compliance with regulations']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['****Customer Feedback Sessions****', 'Regular feedback sessions will be conducted with the ECG Program Manager to identify areas for improvement and ensure customer satisfaction.', 'Monthly', 'Project Manager', 'Customer satisfaction rating > 4/5']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Project', 'Agency', 'Scope', 'Deliverables']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['DoD Contract Closeout Support', 'Department of Defense', 'Review of 200+ contracts', 'PR Packages, Financial Reports, Compliance Verification']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['GSA Schedule Management', 'General Services Administration', 'Management of 50+ GSA Schedules', 'Modification Requests, Pricing Updates, Compliance Audits']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['NIH Grant Administration', 'National Institutes of Health', 'Administration of 20+ research grants', 'Progress Reports, Financial Statements, Compliance Reviews']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Ending table with 4 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table with 4 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Processing 4 cleaned table rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Successfully added table with 4 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Metric', 'Target', 'Data Source', 'Reporting Frequency']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Training Completion Rate', '95%', 'Training Records', 'Monthly']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Post-Training Assessment Score', '80%', 'Assessment Results', 'Monthly']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Number of Export Control Errors', '<5 per month', 'Incident Reporting', 'Monthly']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['User Satisfaction (Training)', '4.0/5.0', 'Post-Training Surveys', 'Quarterly']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Splitting long line (829 characters)
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['KPI', 'Target', 'Actual (DTRS Project)']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Data Submission Accuracy', '99.5%', '99.7%']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Transaction Processing Time', '< 24 hrs', '< 18 hrs']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Regulatory Compliance', '100%', '100%']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Customer Satisfaction', '4.5/5', '4.7/5']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Skillset', 'Available Personnel (within 72 hours)', 'Certification Level', 'Security Clearance']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Export Control Specialist (EAR/ITAR)', '15', 'Certified Export Compliance Professional (CECP), Certified U.S. Export Compliance Officer (CUSECO)', 'Top Secret/SCI, Secret']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Customs Compliance Specialist', '8', 'Certified Customs Specialist (CCS)', 'Secret']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table row: ['International Trade Compliance Analyst', '10', 'Certified International Trade Professional (CITP)', 'Secret']
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Ending table with 4 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Adding table with 4 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Processing 4 cleaned table rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Successfully added table with 4 rows
2025-08-05 13:47:20 | INFO     | VALIDATION | PDF Generator: Generated 573 ReportLab elements after processing 906 lines
2025-08-05 13:47:21 | INFO     | VALIDATION | Calculated 41 pages for main content
2025-08-05 13:47:21 | INFO     | VALIDATION | Added TOC entry: 1.0 Tab A - Proposal Cover/Transmittal Letter at page 3
2025-08-05 13:47:21 | INFO     | VALIDATION | Added TOC entry: 2.0 Tab B - Factor 1 - Staffing & Key Personnel Qualifications at page 11
2025-08-05 13:47:21 | INFO     | VALIDATION | Added TOC subsection: 2.1 Recruitment, Hiring, and Retention Approach at page 12
2025-08-05 13:47:21 | INFO     | VALIDATION | Added TOC subsection: 2.2 Certifications and Training Processes at page 13
2025-08-05 13:47:21 | INFO     | VALIDATION | Added TOC subsection: 2.3 Resume of Proposed Key Personnel at page 14
2025-08-05 13:47:21 | INFO     | VALIDATION | Added TOC subsection: 2.4 Tentative/Contingent Offer Letter at page 15
2025-08-05 13:47:21 | INFO     | VALIDATION | Added TOC entry: 3.0 Tab C - Factor 2 - Management Approach at page 19
2025-08-05 13:47:21 | INFO     | VALIDATION | Added TOC subsection: 3.1 Employee Turnover and Solutions at page 20
2025-08-05 13:47:21 | INFO     | VALIDATION | Added TOC subsection: 3.2 Surge Support Availability at page 21
2025-08-05 13:47:21 | INFO     | VALIDATION | Added TOC subsection: 3.3 Quality Control and Performance Monitoring at page 22
2025-08-05 13:47:21 | INFO     | VALIDATION | Added TOC entry: 4.0 Tab D - Factor 3 - Technical Approach at page 27
2025-08-05 13:47:21 | INFO     | VALIDATION | Added TOC subsection: 4.1 TASK 1 – Program Management and Administration at page 28
2025-08-05 13:47:21 | INFO     | VALIDATION | Added TOC subsection: 4.2 TASK 2 – Information Management at page 29
2025-08-05 13:47:21 | INFO     | VALIDATION | Added TOC subsection: 4.3 TASK 3 – Program Compliance at page 30
2025-08-05 13:47:21 | INFO     | VALIDATION | Added TOC subsection: 4.4 TASK 4 – Training and Outreach at page 31
2025-08-05 13:47:21 | INFO     | VALIDATION | Added TOC subsection: 4.5 TASK 5 – Regulatory Support at page 32
2025-08-05 13:47:21 | INFO     | VALIDATION | Added TOC subsection: 4.6 TASK 6 – Optional – Surge at page 33
2025-08-05 13:47:21 | INFO     | VALIDATION | Added TOC entry: 5.0 Tab E - Factor 4 - Demonstrated Corporate Experience at page 35
2025-08-05 13:47:21 | INFO     | VALIDATION | Added TOC subsection: 5.1 Experience Example 1 at page 36
2025-08-05 13:47:21 | INFO     | VALIDATION | Added TOC subsection: 5.2 Experience Example 2 at page 37
2025-08-05 13:47:21 | INFO     | VALIDATION | Added TOC subsection: 5.3 Experience Example 3 at page 38
2025-08-05 13:47:21 | INFO     | VALIDATION | Generated TOC with accurate page numbers using two-pass approach
2025-08-05 13:47:21 | INFO     | VALIDATION | TOC added after cover page
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Processing 906 lines of markdown content
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['KPI', 'Target', 'Measurement Frequency']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Time to Fill', '< 60 days', 'Monthly']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Quality of Hire', '80% positive performance reviews', 'Annually']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Employee Retention Rate', '> 90%', 'Annually']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Employee Satisfaction Score', '> 4.0 (out of 5)', 'Bi-Annually']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Certification', 'Role Applicability', 'Renewal Frequency', 'Verification Method']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['CompTIA Security+', 'All personnel with system access', 'Every 3 years', 'Digital Badge/Record']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Certified Information Systems Security Professional (CISSP)', 'Security Engineers, Architects, Managers', 'Every 3 years', 'Digital Badge/Record']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Project Management Professional (PMP)', 'Project Managers, Team Leads', 'Every 3 years', 'Digital Badge/Record']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Certified ScrumMaster (CSM)', 'Agile Team Members', 'Every 2 years', 'Digital Badge/Record']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['DHS Anti-Terrorism Awareness Training', 'All personnel accessing DHS facilities/systems', 'Annually', 'Completion Record']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Ending table with 6 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table with 6 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Processing 6 cleaned table rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Successfully added table with 6 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Splitting long line (1007 characters)
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['KPI', 'Target', 'Measurement Frequency', 'Reporting Frequency']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Compliance Review Accuracy', '98%', 'Monthly', 'Monthly']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Deliverable On-Time Rate', '100%', 'Monthly', 'Monthly']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Data Validation Error Rate', '<1%', 'Monthly', 'Monthly']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Customer Satisfaction', '4.5/5 stars', 'Quarterly', 'Quarterly']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['KPI', 'Target', 'Measurement Frequency', 'Reporting Frequency']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Employee Turnover Rate', '< 10%', 'Monthly', 'Quarterly']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Time-to-Fill (Critical)', '< 30 days', 'Monthly', 'Quarterly']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Employee Satisfaction', '> 80%', 'Annually', 'Annually']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Training Hours/Employee', '> 40 hrs', 'Annually', 'Annually']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['KPI', 'Target', 'Measurement Frequency', 'Reporting Frequency']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Surge Support Activation Rate', '< 5%', 'Monthly', 'Quarterly']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Time to Deploy Surge Resource', '< 24 hours', 'Monthly', 'Quarterly']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Customer Satisfaction (Surge Support)', '> 90%', 'Post-Deployment', 'Quarterly']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Impact to Deliverables (Due to Surge)', '< 5%', 'Monthly', 'Quarterly']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['KPI', 'Target', 'Measurement Method', 'Reporting Frequency']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Deliverable Acceptance Rate', '≥ 98%', 'Number of deliverables accepted without revision / Total deliverables submitted', 'Bi-Annual']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Revision Cycle Time', '≤ 2 business days', 'Time elapsed between deliverable submission and final government acceptance', 'Bi-Annual']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Number of Critical Errors', '≤ 1 per quarter', 'Identification of errors requiring significant rework or impacting compliance', 'Bi-Annual']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Adherence to Style Guide', '≥ 95%', 'Percentage of deliverables conforming to the established style guide', 'Bi-Annual']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Tool', 'Purpose', 'Key Features']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Automated Screening Software', 'Restricted Party Screening', 'Real-time screening against multiple restricted party lists, audit trails']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['EDMS', 'Recordkeeping & Document Management', 'Secure storage, version control, audit trails, access control']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['ECCN Classification Tool', 'Commodity Jurisdiction', 'Database of ECCNs, classification guidance, automated lookup']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Ending table with 4 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table with 4 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Processing 4 cleaned table rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Successfully added table with 4 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['****Quality Control Activity****', '****Description****', '****Frequency****', '****Responsible Party****', '****Deliverable/Metric****']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['****Daily Task Review****', 'Each team member will review their completed tasks to ensure accuracy and completeness.', 'Daily', 'Individual Team Member', '100% task completion with no errors']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['****Peer Review****', 'A designated peer will review a sample of completed tasks to identify potential issues and provide constructive feedback.', 'Weekly', 'Senior Analyst', 'Error rate < 2%']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['****Supervisor Review****', 'The Senior/Lead Analyst will review a sample of completed tasks to ensure compliance with all applicable regulations and procedures.', 'Bi-Weekly', 'Senior/Lead Analyst', '100% compliance with regulations']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['****Customer Feedback Sessions****', 'Regular feedback sessions will be conducted with the ECG Program Manager to identify areas for improvement and ensure customer satisfaction.', 'Monthly', 'Project Manager', 'Customer satisfaction rating > 4/5']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Project', 'Agency', 'Scope', 'Deliverables']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['DoD Contract Closeout Support', 'Department of Defense', 'Review of 200+ contracts', 'PR Packages, Financial Reports, Compliance Verification']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['GSA Schedule Management', 'General Services Administration', 'Management of 50+ GSA Schedules', 'Modification Requests, Pricing Updates, Compliance Audits']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['NIH Grant Administration', 'National Institutes of Health', 'Administration of 20+ research grants', 'Progress Reports, Financial Statements, Compliance Reviews']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Ending table with 4 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table with 4 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Processing 4 cleaned table rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Successfully added table with 4 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Metric', 'Target', 'Data Source', 'Reporting Frequency']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Training Completion Rate', '95%', 'Training Records', 'Monthly']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Post-Training Assessment Score', '80%', 'Assessment Results', 'Monthly']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Number of Export Control Errors', '<5 per month', 'Incident Reporting', 'Monthly']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['User Satisfaction (Training)', '4.0/5.0', 'Post-Training Surveys', 'Quarterly']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Splitting long line (829 characters)
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['KPI', 'Target', 'Actual (DTRS Project)']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Data Submission Accuracy', '99.5%', '99.7%']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Transaction Processing Time', '< 24 hrs', '< 18 hrs']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Regulatory Compliance', '100%', '100%']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Customer Satisfaction', '4.5/5', '4.7/5']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Ending table with 5 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table with 5 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Processing 5 cleaned table rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Successfully added table with 5 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Skillset', 'Available Personnel (within 72 hours)', 'Certification Level', 'Security Clearance']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Skipping table separator line
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Export Control Specialist (EAR/ITAR)', '15', 'Certified Export Compliance Professional (CECP), Certified U.S. Export Compliance Officer (CUSECO)', 'Top Secret/SCI, Secret']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['Customs Compliance Specialist', '8', 'Certified Customs Specialist (CCS)', 'Secret']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table row: ['International Trade Compliance Analyst', '10', 'Certified International Trade Professional (CITP)', 'Secret']
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Ending table with 4 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Adding table with 4 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Processing 4 cleaned table rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Successfully added table with 4 rows
2025-08-05 13:47:21 | INFO     | VALIDATION | PDF Generator: Generated 573 ReportLab elements after processing 906 lines
2025-08-05 13:47:21 | INFO     | VALIDATION | Main content added
2025-08-05 13:47:22 | INFO     | VALIDATION | Drew full-page cover image: /tmp/tmpuaxs9w2_.png
2025-08-05 13:47:22 | INFO     | VALIDATION | PDF file successfully saved to: /home/<USER>/Desktop/Development/NEW/GovBD-BackEnd-Python/AIService/generated-pdfs/RFP_Draft_iRiYNgd8RC_8d9e9729-f7bd-44a0-9cf1-777f532a2db2_20250805_134720.pdf
2025-08-05 13:47:22 | INFO     | VALIDATION | Cleaned up temporary file: /tmp/tmpuaxs9w2_.png
2025-08-05 13:47:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:47:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:47:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:47:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:47:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:47:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:47:26 | INFO     | VALIDATION | Lock PROPOSAL_CRITICISM_LOCK acquired for the first time
2025-08-05 13:47:26 | INFO     | VALIDATION | Starting proposal criticism queue processing cycle
2025-08-05 13:47:28 | INFO     | VALIDATION | No new criticism queue items found
2025-08-05 13:47:33 | INFO     | VALIDATION | Lock PROPOSAL_CRITICISM_LOCK released
2025-08-05 13:48:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:48:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:48:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:48:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:48:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:48:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:49:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:49:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:49:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:49:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:49:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:49:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:49:25 | INFO     | VALIDATION | Created criticism queue item for opportunity iRiYNgd8RC
2025-08-05 13:50:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:50:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:50:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:50:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:50:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:50:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:51:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:51:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:51:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:51:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:51:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:51:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:52:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:52:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:52:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:52:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:52:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:52:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:52:26 | INFO     | VALIDATION | Lock PROPOSAL_CRITICISM_LOCK acquired
2025-08-05 13:52:26 | INFO     | VALIDATION | Starting proposal criticism queue processing cycle
2025-08-05 13:52:28 | INFO     | VALIDATION | Found 1 criticism queue items to process
2025-08-05 13:52:30 | INFO     | VALIDATION | Updated criticism queue item 1 status to PROCESSING
2025-08-05 13:52:30 | INFO     | VALIDATION | Processing criticism queue item 1 for opportunity iRiYNgd8RC
2025-08-05 13:52:30 | ERROR    | VALIDATION | Error getting proposal data for criticism: type object 'CustomOppsTable' has no attribute 'opps_id'
2025-08-05 13:52:30 | ERROR    | VALIDATION | Error processing criticism queue item 1: No proposal data found for opportunity iRiYNgd8RC
2025-08-05 13:52:33 | INFO     | VALIDATION | Updated criticism queue item 1 status to FAILED
2025-08-05 13:52:33 | ERROR    | VALIDATION | Failed to process criticism queue item 1: No proposal data found for opportunity iRiYNgd8RC
2025-08-05 13:52:36 | INFO     | VALIDATION | Updated criticism queue item 1 status to FAILED
2025-08-05 13:52:39 | INFO     | VALIDATION | Lock PROPOSAL_CRITICISM_LOCK released
2025-08-05 13:53:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:53:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:53:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:53:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:53:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:53:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:54:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:54:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:54:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:54:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:54:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:54:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:55:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:55:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:55:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:55:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:55:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:55:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:56:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:56:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:56:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:56:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:56:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:56:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:57:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:57:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:57:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:57:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:57:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:57:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:57:26 | INFO     | VALIDATION | Lock PROPOSAL_CRITICISM_LOCK acquired
2025-08-05 13:57:26 | INFO     | VALIDATION | Starting proposal criticism queue processing cycle
2025-08-05 13:57:28 | INFO     | VALIDATION | No new criticism queue items found
2025-08-05 13:57:30 | INFO     | VALIDATION | Lock PROPOSAL_CRITICISM_LOCK released
2025-08-05 13:58:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:58:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:58:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:58:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:58:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:58:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 13:59:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 13:59:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 13:59:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 13:59:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 13:59:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 13:59:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 14:00:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 14:00:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 14:00:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 14:00:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 14:00:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 14:00:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 14:01:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 14:01:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 14:01:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 14:01:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 14:01:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 14:01:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 14:02:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 14:02:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 14:02:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 14:02:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 14:02:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 14:02:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 14:02:25 | INFO     | VALIDATION | Lock PROPOSAL_CRITICISM_LOCK acquired
2025-08-05 14:02:25 | INFO     | VALIDATION | Starting proposal criticism queue processing cycle
2025-08-05 14:02:27 | INFO     | VALIDATION | No new criticism queue items found
2025-08-05 14:02:29 | INFO     | VALIDATION | Lock PROPOSAL_CRITICISM_LOCK released
2025-08-05 14:03:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 14:03:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 14:03:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 14:03:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 14:03:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 14:03:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 14:04:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 14:04:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 14:04:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 14:04:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 14:04:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 14:04:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 14:05:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 14:05:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 14:05:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 14:05:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 14:05:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 14:05:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 14:06:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 14:06:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 14:06:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 14:06:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 14:06:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 14:06:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 14:07:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 14:07:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 14:07:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 14:07:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 14:07:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 14:07:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 14:07:25 | INFO     | VALIDATION | Lock PROPOSAL_CRITICISM_LOCK acquired
2025-08-05 14:07:25 | INFO     | VALIDATION | Starting proposal criticism queue processing cycle
2025-08-05 14:07:26 | INFO     | VALIDATION | No new criticism queue items found
2025-08-05 14:07:28 | INFO     | VALIDATION | Lock PROPOSAL_CRITICISM_LOCK released
2025-08-05 14:08:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 14:08:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 14:08:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 14:08:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 14:08:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 14:08:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 14:09:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 14:09:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 14:09:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 14:09:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 14:09:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 14:09:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 14:10:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 14:10:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 14:10:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 14:10:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 14:10:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 14:10:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 14:11:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 14:11:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 14:11:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 14:11:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 14:11:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 14:11:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 14:12:23 | INFO     | VALIDATION | Proposal scheduler is disabled, skipping processing
2025-08-05 14:12:23 | INFO     | VALIDATION | Custom opps scheduler is disabled, skipping processing
2025-08-05 14:12:23 | INFO     | VALIDATION | SAM opps scheduler is disabled, skipping processing
2025-08-05 14:12:23 | INFO     | VALIDATION | Client process queue scheduler is disabled, skipping processing
2025-08-05 14:12:23 | INFO     | VALIDATION | Datametastore queue scheduler is disabled, skipping processing
2025-08-05 14:12:23 | INFO     | VALIDATION | Simulation scheduler is disabled, skipping processing
2025-08-05 14:12:27 | INFO     | VALIDATION | Lock PROPOSAL_CRITICISM_LOCK acquired
2025-08-05 14:12:27 | INFO     | VALIDATION | Starting proposal criticism queue processing cycle
2025-08-05 14:12:29 | INFO     | VALIDATION | No new criticism queue items found
2025-08-05 14:12:31 | INFO     | VALIDATION | Lock PROPOSAL_CRITICISM_LOCK released
2025-08-05 14:13:18 | INFO     | VALIDATION | Stopping all schedulers...
2025-08-05 14:13:18 | INFO     | VALIDATION | Proposal scheduler stopped
2025-08-05 14:13:18 | INFO     | VALIDATION | Proposal criticism scheduler stopped successfully
2025-08-05 14:13:18 | INFO     | VALIDATION | Custom opps scheduler stopped
2025-08-05 14:13:18 | INFO     | VALIDATION | SAM opps scheduler stopped
2025-08-05 14:13:18 | INFO     | VALIDATION | Client process queue scheduler stopped
2025-08-05 14:13:18 | INFO     | VALIDATION | Datametastore queue scheduler stopped
2025-08-05 14:13:18 | INFO     | VALIDATION | Simulation scheduler stopped
2025-08-05 14:13:18 | INFO     | VALIDATION | All schedulers stopped
2025-08-05 14:13:19 | INFO     | VALIDATION | Application shutdown completed
