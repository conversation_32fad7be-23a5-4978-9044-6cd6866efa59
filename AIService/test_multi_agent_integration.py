#!/usr/bin/env python3
"""
Test Multi-Agent Integration with ProposalOutlineService

This script demonstrates how the multi-agent system integrates with the existing
ProposalOutlineService to replace the traditional generate_draft method.
"""

import asyncio
import json
import logging
from services.proposal.outline import ProposalOutlineService
from services.proposal.utilities import ProposalUtilities

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logger = logging.getLogger("MULTI_AGENT_INTEGRATION_TEST")


async def test_multi_agent_integration():
    """Test the multi-agent integration with ProposalOutlineService"""
    
    print("🧪 Testing Multi-Agent Integration")
    print("=" * 50)
    
    # Test parameters
    opportunity_id = "TEST_MULTI_AGENT_001"
    tenant_id = "TEST_TENANT_001"
    source = "custom"
    client_short_name = "TestClient"
    tenant_metadata = "Test tenant metadata for multi-agent system"
    
    # Sample table of contents
    table_of_contents = [
        {
            "title": "Executive Summary",
            "description": "High-level overview of the proposal",
            "content": "Provide a compelling executive summary that highlights key benefits and value proposition."
        },
        {
            "title": "Technical Approach",
            "description": "Detailed technical solution and methodology",
            "content": "Describe the technical approach, architecture, and implementation methodology."
        },
        {
            "title": "Management Plan",
            "description": "Project management and organizational approach",
            "content": "Detail the project management methodology, team structure, and delivery approach."
        }
    ]
    
    print(f"📋 Test Configuration:")
    print(f"   Opportunity ID: {opportunity_id}")
    print(f"   Tenant ID: {tenant_id}")
    print(f"   Client: {client_short_name}")
    print(f"   Sections: {len(table_of_contents)}")
    
    try:
        # Initialize the service
        print(f"\n🔧 Initializing ProposalOutlineService...")
        outline_service = ProposalOutlineService()
        print(f"✅ Service initialized successfully")
        
        # Test the new multi-agent method
        print(f"\n🚀 Testing Multi-Agent Generation...")
        
        result = await outline_service.generate_draft_multi_agent(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            client_short_name=client_short_name,
            tenant_metadata=tenant_metadata,
            table_of_contents=table_of_contents
        )
        
        print(f"✅ Multi-agent generation completed!")
        
        # Analyze results
        print(f"\n📊 Results Analysis:")
        print(f"   Generation Method: {result.get('generation_method', 'unknown')}")
        print(f"   Total Sections: {result.get('generation_summary', {}).get('total_sections', 0)}")
        print(f"   Successful Sections: {result.get('generation_summary', {}).get('successful_sections', 0)}")
        print(f"   Failed Sections: {result.get('generation_summary', {}).get('failed_sections', 0)}")
        print(f"   Success Rate: {result.get('success_rate', 0):.1f}%")
        
        if result.get('average_quality_score'):
            print(f"   Average Quality Score: {result.get('average_quality_score'):.1f}/10")
        
        # Show section results
        sections = result.get('sections', {})
        print(f"\n📝 Section Results:")
        for section_title, section_data in sections.items():
            status = "✅" if section_data.get('content') else "❌"
            quality = section_data.get('quality_score', 'N/A')
            method = section_data.get('generation_method', 'unknown')
            
            print(f"   {status} {section_title}")
            print(f"      Method: {method}")
            print(f"      Quality: {quality}")
            
            if section_data.get('content'):
                content_length = len(section_data['content'])
                print(f"      Content: {content_length} characters")
            elif section_data.get('error'):
                print(f"      Error: {section_data['error']}")
        
        # Show agent performance if available
        agent_performance = result.get('generation_summary', {}).get('agent_performance', {})
        if agent_performance:
            print(f"\n🤖 Agent Performance:")
            for agent_role, performance in agent_performance.items():
                successes = performance.get('successes', 0)
                failures = performance.get('failures', 0)
                total_time = performance.get('total_time', 0)
                
                print(f"   {agent_role}:")
                print(f"      Successes: {successes}")
                print(f"      Failures: {failures}")
                print(f"      Total Time: {total_time:.2f}s")
        
        # Save results for inspection
        timestamp = "test_multi_agent"
        filename = f"test-multi-agent-result-{timestamp}.json"
        ProposalUtilities.save_json_to_file(result, filename)
        print(f"\n💾 Results saved to: {filename}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Multi-agent integration test failed: {e}")
        logger.error(f"Integration test failed: {e}")
        return False


async def compare_methods():
    """Compare traditional vs multi-agent generation methods"""
    
    print("\n🔍 Comparing Generation Methods")
    print("=" * 40)
    
    # Test parameters
    opportunity_id = "COMPARE_TEST_001"
    tenant_id = "COMPARE_TENANT_001"
    source = "custom"
    client_short_name = "CompareClient"
    tenant_metadata = "Comparison test metadata"
    
    # Simple table of contents for comparison
    table_of_contents = [
        {
            "title": "Cover Letter",
            "description": "Professional cover letter",
            "content": "Create a compelling cover letter for this proposal."
        }
    ]
    
    outline_service = ProposalOutlineService()
    
    print(f"📋 Comparison Test:")
    print(f"   Testing both traditional and multi-agent methods")
    print(f"   Section: Cover Letter")
    
    # Test multi-agent method
    print(f"\n🚀 Testing Multi-Agent Method...")
    try:
        multi_agent_result = await outline_service.generate_draft_multi_agent(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            client_short_name=client_short_name,
            tenant_metadata=tenant_metadata,
            table_of_contents=table_of_contents
        )
        
        multi_agent_success = multi_agent_result.get('generation_summary', {}).get('successful_sections', 0) > 0
        print(f"   Multi-Agent Result: {'✅ Success' if multi_agent_success else '❌ Failed'}")
        
        if multi_agent_success:
            quality_score = multi_agent_result.get('average_quality_score')
            print(f"   Quality Score: {quality_score if quality_score else 'N/A'}")
        
    except Exception as e:
        print(f"   Multi-Agent Result: ❌ Exception - {e}")
        multi_agent_success = False
    
    # Test traditional method
    print(f"\n🔄 Testing Traditional Method...")
    try:
        traditional_result = await outline_service.generate_draft(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            client_short_name=client_short_name,
            tenant_metadata=tenant_metadata,
            table_of_contents=table_of_contents
        )
        
        traditional_success = bool(traditional_result.get('sections'))
        print(f"   Traditional Result: {'✅ Success' if traditional_success else '❌ Failed'}")
        
    except Exception as e:
        print(f"   Traditional Result: ❌ Exception - {e}")
        traditional_success = False
    
    # Summary
    print(f"\n📊 Comparison Summary:")
    print(f"   Multi-Agent: {'✅' if multi_agent_success else '❌'}")
    print(f"   Traditional: {'✅' if traditional_success else '❌'}")
    
    if multi_agent_success and not traditional_success:
        print(f"   🎯 Multi-agent system provides better reliability!")
    elif not multi_agent_success and traditional_success:
        print(f"   ⚠️ Traditional method more reliable (likely LLM issues)")
    elif multi_agent_success and traditional_success:
        print(f"   ✅ Both methods working (LLM is available)")
    else:
        print(f"   ❌ Both methods failed (LLM connectivity issues)")


async def main():
    """Main test function"""
    
    print("🚀 Multi-Agent Integration Testing")
    print("=" * 60)
    print("This test demonstrates how the multi-agent system integrates")
    print("with the existing ProposalOutlineService as a replacement")
    print("for the traditional generate_draft method.")
    print("=" * 60)
    
    try:
        # Test integration
        integration_success = await test_multi_agent_integration()
        
        # Compare methods
        await compare_methods()
        
        print("\n" + "=" * 60)
        if integration_success:
            print("🎉 MULTI-AGENT INTEGRATION SUCCESSFUL!")
            print("✅ The new generate_draft_multi_agent method is working")
            print("✅ Multi-agent system properly integrated")
            print("🎯 Ready to replace traditional generation!")
        else:
            print("⚠️ MULTI-AGENT INTEGRATION ISSUES")
            print("❌ Check LLM connectivity and multi-agent system")
            print("🔧 Review logs for specific error details")
        
        print("\n💡 Usage in your pipeline:")
        print("   Replace: outline_service.generate_draft(...)")
        print("   With:    outline_service.generate_draft_multi_agent(...)")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        logger.error(f"Test suite error: {e}")


if __name__ == "__main__":
    asyncio.run(main())
