import asyncio
from controllers.customer.tenant_controller import Tenant<PERSON>ontroller
from services.proposal.utilities import ProposalUtilities
from services.proposal.outline import ProposalOutlineService
from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from database import get_customer_db
import json

async def main():

    opportunity_id = "iRiYNgd8RC"
    tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
    client = "adeptengineeringsolutions"
    source = "custom"

    outline_service = ProposalOutlineService()
    custom_controller = CustomOpportunitiesController
    tenant_controller = TenantController
    

    
    
    async for db in get_customer_db():
        record = await custom_controller.get_table_of_contents(db, opportunity_id)
        table_of_contents = json.loads(str(record[0])) if record and record[0] is not None else []
        tenant = await tenant_controller.get_by_tenant_id(db, tenant_id)
        tenant_metadata = f"{tenant}"
        break
    
    
    table_of_contents = ProposalUtilities.read_json_from_file("table-of-contents.json").get("table_of_contents", [])

    proposal_draft = await outline_service.generate_draft(
        opportunity_id=opportunity_id,
        tenant_id=tenant_id,
        source=source,
        table_of_contents=table_of_contents,
        tenant_metadata=tenant_metadata,
        client_short_name=client
    )

    ProposalUtilities.save_json_to_file(proposal_draft, "proposal-draft-3.json")
    
    
    
    


    
    
    toc = ProposalUtilities.read_json_from_file("table-of-contents.json")
    proposal_draft = ProposalUtilities.read_json_from_file("proposal-draft-3.json")


    async for db in get_customer_db():
        await custom_controller.update_by_opportunity_id(db, opportunity_id, {
            "toc_text": json.dumps(toc.get("table_of_contents", [])),
            "draft": json.dumps(proposal_draft.get("draft", []))
        })
        break

    print("Updated Draft in Table")
    
    


if __name__ == "__main__":
    asyncio.run(main())