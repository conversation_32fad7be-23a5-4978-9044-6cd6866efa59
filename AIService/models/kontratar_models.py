from datetime import datetime, date

from database import Base
from sqlalchemy import (BigInteger, Boolean, Column, DateTime,
                         Integer, String, Text, LargeBinary)

def _json_serialize(value):
    if isinstance(value, (datetime, date)):
        return value.isoformat()
    if isinstance(value, bytes):
        return value.decode('utf-8', errors='replace')
    if isinstance(value, (list, tuple)):
        return [_json_serialize(v) for v in value]
    if isinstance(value, dict):
        return {k: _json_serialize(v) for k, v in value.items()}
    return value

class BaseModelMixin:
    def as_dict(self):
        result = {}
        table = getattr(self, "__table__", None)
        if table is not None:
            for column in table.columns:
                value = getattr(self, column.name)
                result[column.name] = _json_serialize(value)
        return result

# Kontratar Main Schema Models
class ChromaDBInstanceMapping(Base, BaseModelMixin):
    __tablename__ = "chromadb_instance_mapping"
    __table_args__ = {"schema": "kontratar_main"}
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    unique_id = Column(String(255), nullable=False)
    tenant_id = Column(String(255), nullable=False)
    chroma_instance_url = Column(String(255), nullable=False)
    collection_name = Column(String(255), nullable=False)
    created_date = Column(DateTime, nullable=False)
    last_accessed_date = Column(DateTime)
    status = Column(String(50), nullable=False)
    version = Column(Integer, nullable=True)


class Lock(Base, BaseModelMixin):
    __tablename__ = "lock"
    __table_args__ = {"schema": "kontratar_main"}
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    lock_id = Column(String(255), nullable=False)
    is_processing = Column(Boolean, nullable=False)
    lock_acquired_at = Column(DateTime)
    next_scheduled_run = Column(DateTime)
    type = Column(String)
    tenant_id = Column(String)


class OppsDocument(Base, BaseModelMixin):
    __tablename__ = "opps_document"
    __table_args__ = {"schema": "kontratar_main"}
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    opps_source = Column(String(255), nullable=False)
    opps_id = Column(String(255))
    opps_data = Column(LargeBinary)  # bytea in PostgreSQL
    opps_file_name = Column(String(255))
    created_date = Column(DateTime, default=datetime.utcnow)
    opps_data_size = Column(BigInteger)
    check_sum = Column(LargeBinary)  # bytea in PostgreSQL
    update_date = Column(DateTime)
    status = Column(String)
    opps_data_text = Column(Text)
    elastic_search_index_doc = Column(Text)
    batch_id = Column(String)


class OppsTable(Base, BaseModelMixin):
    __tablename__ = "oppstable"
    __table_args__ = {"schema": "kontratar_main"}
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    title = Column(String, nullable=False)
    description = Column(String)
    posted_date = Column(DateTime)
    archive_date = Column(DateTime)
    naics_code = Column(String(255))
    naics_codes = Column(String(255))
    type_of_set_aside = Column(String(255))
    type_of_set_aside_description = Column(String(255))
    notice_id = Column(String(255))
    solicitation_number = Column(String(255))
    full_parent_path_name = Column(String(255))
    full_parent_path_code = Column(String(255))
    type_op = Column(String(255))
    base_type_op = Column(String(255))
    archive_type = Column(String(255))
    classification_code = Column(String)
    point_of_contact_name = Column(String)
    point_of_contact_email = Column(String(255))
    point_of_contact_phone = Column(String(255))
    place_of_performance_city_name = Column(String(255))
    place_of_performance_state_name = Column(String(255))
    place_of_performance_zip = Column(String(255))
    place_of_performance_country_name = Column(String(255))
    ulink = Column(String(255))
    created_date = Column(DateTime, default=datetime.utcnow)
    description_text = Column(String)
    agency_code = Column(String(255))
    last_mod_date = Column(DateTime)
    summary_text = Column(Text)
    requirement_text = Column(Text)
    grading_criteria_text = Column(Text)
    response_dead_line = Column(DateTime)
    point_of_contact_fax = Column(String(255))
    point_of_contact_type = Column(String(255))
    point_of_contact_title = Column(String(255))
    office_address_zipcode = Column(String(255))
    office_address_city = Column(String(255))
    office_address_country_code = Column(String(255))
    office_address_state = Column(String(255))
    active = Column(String(255))
    award_date = Column(String(255))
    award_number = Column(String(255))
    award_amount = Column(String(255))
    award_awardee_name = Column(String(255))
    award_awardee_location_street_address = Column(String(255))
    award_awardee_location_city_code = Column(String(255))
    award_awardee_location_city_name = Column(String(255))
    award_awardee_location_state_code = Column(String(255))
    award_awardee_location_state_name = Column(String(255))
    award_awardee_location_zip = Column(String(255))
    award_awardee_location_country_code = Column(String(255))
    award_awardee_location_country_name = Column(String(255))
    award_awardee_uei_sam = Column(String(255))
    award_awardee_duns = Column(String(255))
    award_awardee_cage_code = Column(String(255))
    additional_info_link = Column(String(255))
    point_of_contact_full_name = Column(String(255))
    organization_type = Column(String(255))
    place_of_performance_city_code = Column(String(255))
    place_of_performance_state_code = Column(String(255))
    place_of_performance_country_code = Column(String(255))
    place_of_performance_city = Column(String)
    place_of_performance_state = Column(String)
    place_of_performance_country = Column(String)
    score_status = Column(String)
    score_update_time = Column(DateTime)
    vector_status = Column(String)
    vector_status_time = Column(DateTime)
    toc_text = Column(Text)
    toc_text2 = Column(Text)
    toc_text3 = Column(Text)
    toc_text4 = Column(Text)
    toc_text5 = Column(Text)
    format_compliance = Column(Text)
    structure_compliance = Column(Text)
    content_compliance = Column(Text)
    cover_page_fields = Column(Text)
    keywords = Column(Text)
    key_personnel = Column(Text)
    status = Column(String)
    proposal_outline_1 = Column(Text)
    proposal_outline_2 = Column(Text)
    proposal_outline_3 = Column(Text)
    proposal_outline_4 = Column(Text)
    proposal_outline_5 = Column(Text)
    


class EBUYOppsTable(Base, BaseModelMixin):
    __tablename__ = "ebuy_oppstable"
    __table_args__ = {"schema": "kontratar_main"}
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    rfq_id = Column(String(255))
    title = Column(String(255))
    description = Column(String)
    reference_id = Column(String(255))
    issue_date = Column(DateTime)
    close_date = Column(DateTime)
    delivery_info = Column(String(255))
    contact_info = Column(String(255))
    shipping_address = Column(String(255))
    description_text = Column(String(255))
    posted_date = Column(DateTime)
    naics_code = Column(String(255))
    created_date = Column(DateTime, default=datetime.utcnow)
    buyer = Column(String(255))
    tenant_id = Column(String(255))
    last_mod_date = Column(DateTime)
    summary_text = Column(String(255))
    requirement_text = Column(String(255))
    grading_criteria_text = Column(String(255))
    requirement_full_text = Column(String(255))
    opp_type = Column(String)
    toc_text = Column(Text)
    toc_text_2 = Column(Text)
    toc_text_3 = Column(Text)
    toc_text_4 = Column(Text)
    toc_text_5 = Column(Text)
    format_compliance = Column(Text)
    structure_compliance = Column(Text)
    content_compliance = Column(Text)
    cover_page_fields = Column(Text)
    proposal_outline = Column(Text)
    keywords = Column(Text)
    key_personnel = Column(Text)


class OpportunityTableInfo(Base, BaseModelMixin):
    __tablename__ = "oppstableinfo"
    __table_args__ = {"schema": "kontratar_main"}

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    opps_id = Column(String(255), nullable=False)
    status = Column(String(255), nullable=False)
    opps_raw_text = Column(Text)
    created_date = Column(DateTime, nullable=False)

class SamOpportunityQueue(Base, BaseModelMixin):
    __tablename__ = "sam_opps_queue"
    __table_args__ = {"schema": "kontratar_main"}

    id = Column(BigInteger, primary_key=True)
    opps_id = Column(String(255), nullable=False)
    status = Column(String(255), nullable=False)
    created_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_date = Column(DateTime, nullable=False, default=datetime.utcnow)


# Kontratar Global Schema Models

class Agencies(Base, BaseModelMixin):
    __tablename__ = "agencies"
    __table_args__ = {"schema": "kontratar_global"}
    
    agency_id = Column(BigInteger, primary_key=True, autoincrement=True)
    agency_name = Column(String(255))
    agency_code = Column(String(255), nullable=False)
    agency_abbrev = Column(String(50))


class SubAgencies(Base, BaseModelMixin):
    __tablename__ = "subagencies"
    __table_args__ = {"schema": "kontratar_global"}
    
    subagency_id = Column(BigInteger, primary_key=True, autoincrement=True)
    parent_agency_code = Column(String(255))
    subagency_name = Column(String(255))
    subagency_abbrev = Column(String)
    sub_agency_code = Column(String(255))


class NAICSCode(Base, BaseModelMixin):
    __tablename__ = "naics_code"
    __table_args__ = {"schema": "kontratar_global"}
    
    code_year = Column("naics_code_yyyy", String(200), primary_key=True)
    code_2022 = Column("naics_code_2022", String(200))
    title_2022 = Column("naics_title_2022", String(200)) 