#!/usr/bin/env python3
"""
Test script for agent state serialization and deserialization.

This script tests the serialization capabilities of the multi-agent system
to ensure state can be properly saved and restored.
"""

import asyncio
import json
import logging
from datetime import datetime
from services.proposal.multi_agent.agent_state import (
    DraftState, AgentResult, AgentRole, SectionType, 
    ComplexityLevel, ContextData, WorkflowPlan
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logger = logging.getLogger("SERIALIZATION_TEST")


def test_agent_result_serialization():
    """Test AgentResult serialization and deserialization"""
    
    print("🧪 Testing AgentResult Serialization")
    print("=" * 40)
    
    # Create test AgentResult
    original_result = AgentResult(
        agent_role=AgentRole.TECHNICAL_SPECIALIST,
        success=True,
        content="Test technical content for serialization",
        metadata={
            'section_type': 'technical_approach',
            'complexity': 'high',
            'processing_time': 45.2
        },
        processing_time=45.2
    )
    
    try:
        # Test serialization
        serialized = original_result.to_dict()
        print("✅ AgentResult serialization successful")
        print(f"   Keys: {list(serialized.keys())}")
        
        # Test deserialization
        restored_result = AgentResult.from_dict(serialized)
        print("✅ AgentResult deserialization successful")
        
        # Verify data integrity
        assert original_result.agent_role == restored_result.agent_role
        assert original_result.success == restored_result.success
        assert original_result.content == restored_result.content
        assert original_result.metadata == restored_result.metadata
        
        print("✅ Data integrity verified")
        return True
        
    except Exception as e:
        print(f"❌ AgentResult serialization failed: {e}")
        return False


def test_context_data_serialization():
    """Test ContextData serialization and deserialization"""
    
    print("\n🧪 Testing ContextData Serialization")
    print("=" * 40)
    
    # Create test ContextData
    original_context = ContextData(
        opportunity_context=[
            "Opportunity requirement 1",
            "Opportunity requirement 2"
        ],
        client_context=[
            "Client background info",
            "Client preferences"
        ],
        technical_context=[
            "Technical capability 1",
            "Technical capability 2"
        ]
    )
    
    try:
        # Test serialization
        serialized = original_context.to_dict()
        print("✅ ContextData serialization successful")
        print(f"   Context categories: {len(serialized)}")
        
        # Test deserialization
        restored_context = ContextData.from_dict(serialized)
        print("✅ ContextData deserialization successful")
        
        # Verify data integrity
        assert original_context.opportunity_context == restored_context.opportunity_context
        assert original_context.client_context == restored_context.client_context
        assert original_context.technical_context == restored_context.technical_context
        
        print("✅ Data integrity verified")
        return True
        
    except Exception as e:
        print(f"❌ ContextData serialization failed: {e}")
        return False


def test_workflow_plan_serialization():
    """Test WorkflowPlan serialization and deserialization"""
    
    print("\n🧪 Testing WorkflowPlan Serialization")
    print("=" * 40)
    
    # Create test WorkflowPlan
    original_plan = WorkflowPlan(
        primary_specialist=AgentRole.TECHNICAL_SPECIALIST,
        supporting_agents=[
            AgentRole.CONTEXT_RETRIEVAL,
            AgentRole.COMPLIANCE_AGENT,
            AgentRole.QUALITY_ASSURANCE
        ],
        complexity_level=ComplexityLevel.HIGH,
        estimated_time=120.5,
        special_instructions=[
            "Focus on technical depth",
            "Include security considerations",
            "Address scalability requirements"
        ]
    )
    
    try:
        # Test serialization
        serialized = original_plan.to_dict()
        print("✅ WorkflowPlan serialization successful")
        print(f"   Primary specialist: {serialized['primary_specialist']}")
        print(f"   Supporting agents: {len(serialized['supporting_agents'])}")
        
        # Test deserialization
        restored_plan = WorkflowPlan.from_dict(serialized)
        print("✅ WorkflowPlan deserialization successful")
        
        # Verify data integrity
        assert original_plan.primary_specialist == restored_plan.primary_specialist
        assert original_plan.supporting_agents == restored_plan.supporting_agents
        assert original_plan.complexity_level == restored_plan.complexity_level
        assert original_plan.estimated_time == restored_plan.estimated_time
        assert original_plan.special_instructions == restored_plan.special_instructions
        
        print("✅ Data integrity verified")
        return True
        
    except Exception as e:
        print(f"❌ WorkflowPlan serialization failed: {e}")
        return False


def test_draft_state_serialization():
    """Test complete DraftState serialization and deserialization"""
    
    print("\n🧪 Testing DraftState Serialization")
    print("=" * 40)
    
    # Create comprehensive test DraftState
    original_state = DraftState(
        opportunity_id="TEST_OPP_123",
        tenant_id="TEST_TENANT_456",
        section_type=SectionType.TECHNICAL_APPROACH,
        section_content="Detailed technical requirements for testing",
        client_short_name="TestClient"
    )
    
    # Add context data
    context_data = ContextData(
        opportunity_context=["Req 1", "Req 2"],
        client_context=["Client info 1", "Client info 2"],
        technical_context=["Tech 1", "Tech 2"]
    )
    original_state.update_context(context_data)
    
    # Add workflow plan
    workflow_plan = WorkflowPlan(
        primary_specialist=AgentRole.TECHNICAL_SPECIALIST,
        supporting_agents=[AgentRole.CONTEXT_RETRIEVAL],
        complexity_level=ComplexityLevel.MEDIUM,
        estimated_time=60.0
    )
    original_state.set_workflow_plan(workflow_plan)
    
    # Add agent results
    agent_result = AgentResult(
        agent_role=AgentRole.TECHNICAL_SPECIALIST,
        success=True,
        content="Generated technical content",
        metadata={'test': 'data'},
        processing_time=30.5
    )
    original_state.add_agent_result(agent_result)
    
    # Set final content
    original_state.set_final_content("Final generated content", 8.5)
    
    try:
        # Test serialization
        serialized = original_state.to_dict()
        print("✅ DraftState serialization successful")
        print(f"   Opportunity ID: {serialized['opportunity_id']}")
        print(f"   Section type: {serialized['section_type']}")
        print(f"   Has context: {serialized['context_data'] is not None}")
        print(f"   Has workflow plan: {serialized['workflow_plan'] is not None}")
        print(f"   Agent results: {len(serialized['agent_results'])}")
        print(f"   Quality score: {serialized['quality_score']}")
        
        # Test JSON serialization (important for storage)
        json_str = json.dumps(serialized, indent=2)
        print(f"✅ JSON serialization successful ({len(json_str)} chars)")
        
        # Test deserialization
        restored_state = DraftState.from_dict(serialized)
        print("✅ DraftState deserialization successful")
        
        # Verify data integrity
        assert original_state.opportunity_id == restored_state.opportunity_id
        assert original_state.tenant_id == restored_state.tenant_id
        assert original_state.section_type == restored_state.section_type
        assert original_state.section_content == restored_state.section_content
        assert original_state.client_short_name == restored_state.client_short_name
        assert original_state.final_content == restored_state.final_content
        assert original_state.quality_score == restored_state.quality_score
        
        # Verify context data
        if original_state.context_data and restored_state.context_data:
            assert (original_state.context_data.opportunity_context == 
                   restored_state.context_data.opportunity_context)
        
        # Verify workflow plan
        if original_state.workflow_plan and restored_state.workflow_plan:
            assert (original_state.workflow_plan.primary_specialist == 
                   restored_state.workflow_plan.primary_specialist)
        
        # Verify agent results
        assert len(original_state.agent_results) == len(restored_state.agent_results)
        
        print("✅ Complete data integrity verified")
        return True
        
    except Exception as e:
        print(f"❌ DraftState serialization failed: {e}")
        return False


def test_json_compatibility():
    """Test JSON compatibility for storage systems"""
    
    print("\n🧪 Testing JSON Compatibility")
    print("=" * 35)
    
    # Create a complex state
    state = DraftState(
        opportunity_id="JSON_TEST_001",
        tenant_id="JSON_TENANT_001",
        section_type=SectionType.MANAGEMENT_PLAN,
        section_content="JSON compatibility test requirements",
        client_short_name="JSONClient"
    )
    
    # Add various data types
    context = ContextData(
        opportunity_context=["JSON test context"],
        client_context=["JSON client info"]
    )
    state.update_context(context)
    
    result = AgentResult(
        agent_role=AgentRole.MANAGEMENT_SPECIALIST,
        success=True,
        content="JSON test content",
        metadata={
            'numbers': [1, 2, 3],
            'nested': {'key': 'value'},
            'boolean': True,
            'null_value': None
        }
    )
    state.add_agent_result(result)
    
    try:
        # Serialize to dict
        state_dict = state.to_dict()
        
        # Convert to JSON string
        json_str = json.dumps(state_dict, indent=2)
        print(f"✅ JSON string creation successful ({len(json_str)} chars)")
        
        # Parse back from JSON
        parsed_dict = json.loads(json_str)
        print("✅ JSON parsing successful")
        
        # Restore state from parsed JSON
        restored_state = DraftState.from_dict(parsed_dict)
        print("✅ State restoration from JSON successful")
        
        # Verify key data
        assert state.opportunity_id == restored_state.opportunity_id
        assert state.section_type == restored_state.section_type
        
        print("✅ JSON round-trip compatibility verified")
        return True
        
    except Exception as e:
        print(f"❌ JSON compatibility test failed: {e}")
        return False


async def main():
    """Main test function"""
    
    print("🚀 Testing Multi-Agent State Serialization")
    print("=" * 60)
    print("This test validates that agent states can be properly")
    print("serialized and deserialized for persistence and recovery.")
    print("=" * 60)
    
    test_results = []
    
    # Run all serialization tests
    tests = [
        ("AgentResult Serialization", test_agent_result_serialization),
        ("ContextData Serialization", test_context_data_serialization),
        ("WorkflowPlan Serialization", test_workflow_plan_serialization),
        ("DraftState Serialization", test_draft_state_serialization),
        ("JSON Compatibility", test_json_compatibility)
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            test_results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 SERIALIZATION TEST RESULTS")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in test_results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} | {test_name}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL SERIALIZATION TESTS PASSED!")
        print("✅ Agent states can be properly serialized")
        print("✅ JSON compatibility verified")
        print("🎯 Ready for persistence and recovery!")
    else:
        print("❌ SOME SERIALIZATION TESTS FAILED!")
        print("⚠️ State persistence may have issues")
        print("🔧 Review failed tests and fix serialization")
    
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
