#!/usr/bin/env python3
"""
Test script to verify that all fallbacks have been removed from the multi-agent system.

This script validates that the system now fails cleanly when LLM is unavailable,
rather than generating low-quality fallback content.
"""

import asyncio
import logging
from services.proposal.multi_agent.workflow import MultiAgentWorkflow
from services.proposal.multi_agent.agent_state import SectionType
from services.proposal.multi_agent.llm_config import LLMEndpoint
from services.proposal.multi_agent.robust_llm import RobustLLMManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logger = logging.getLogger("NO_FALLBACKS_TEST")


async def test_workflow_with_unavailable_llm():
    """Test that workflow fails cleanly when LLM is unavailable"""
    
    print("🧪 Testing Workflow with Unavailable LLM")
    print("=" * 50)
    
    # Create workflow
    workflow = MultiAgentWorkflow()
    
    # Test parameters
    test_params = {
        'opportunity_id': 'TEST_OPP_001',
        'tenant_id': 'TEST_TENANT_001', 
        'section_type': 'technical_approach',
        'section_content': 'Test technical requirements for validation',
        'client_short_name': 'TestClient'
    }
    
    try:
        print("🚀 Starting workflow with test parameters...")
        print(f"   Section Type: {test_params['section_type']}")
        print(f"   Opportunity: {test_params['opportunity_id']}")
        
        # This should fail cleanly if LLM is unavailable
        result = await workflow.generate_section_content(**test_params)
        
        if result['success']:
            print("✅ Workflow completed successfully")
            print(f"📝 Generated content length: {len(result.get('content', ''))}")
            print(f"🎯 Quality score: {result.get('quality_score', 'N/A')}")
            
            # Verify content is not fallback
            content = result.get('content', '')
            if _is_fallback_content(content):
                print("❌ FAILURE: Detected fallback content!")
                return False
            else:
                print("✅ Content appears to be high-quality (not fallback)")
                
        else:
            print("⚠️ Workflow failed cleanly (expected if LLM unavailable)")
            print(f"   Error: {result.get('error', 'Unknown error')}")
            print("✅ GOOD: No fallback content generated")
            
            # Verify no content was generated
            if result.get('content'):
                print("❌ FAILURE: Content generated despite failure!")
                return False
            else:
                print("✅ Confirmed: No content generated on failure")
        
        return True
        
    except Exception as e:
        print(f"⚠️ Workflow raised exception (expected if LLM unavailable): {e}")
        print("✅ GOOD: Clean exception handling, no fallback content")
        return True


def _is_fallback_content(content: str) -> bool:
    """Check if content appears to be fallback/template content"""
    if not content:
        return False
    
    fallback_indicators = [
        "template",
        "placeholder", 
        "fallback",
        "generic content",
        "default response",
        "[INSERT",
        "TODO:",
        "PLACEHOLDER"
    ]
    
    content_lower = content.lower()
    return any(indicator in content_lower for indicator in fallback_indicators)


async def test_individual_agents():
    """Test individual agents to ensure they fail cleanly"""
    
    print("\n🔍 Testing Individual Agents")
    print("=" * 40)
    
    from services.proposal.multi_agent.coordinator_agent import CoordinatorAgent
    from services.proposal.multi_agent.cover_letter_specialist import CoverLetterSpecialist
    from services.proposal.multi_agent.technical_specialist import TechnicalSpecialist
    from services.proposal.multi_agent.agent_state import DraftState
    
    # Create test state
    test_state = DraftState(
        opportunity_id="TEST_001",
        tenant_id="TENANT_001", 
        section_type=SectionType.TECHNICAL_APPROACH,
        section_content="Test requirements",
        client_short_name="TestClient"
    )
    
    agents_to_test = [
        ("CoordinatorAgent", CoordinatorAgent()),
        ("CoverLetterSpecialist", CoverLetterSpecialist()),
        ("TechnicalSpecialist", TechnicalSpecialist())
    ]
    
    all_passed = True
    
    for agent_name, agent in agents_to_test:
        print(f"\n🧪 Testing {agent_name}...")
        
        try:
            result = await agent.process(test_state)
            
            if result.success:
                print(f"✅ {agent_name} completed successfully")
                
                # Check for fallback content
                if result.content and _is_fallback_content(result.content):
                    print(f"❌ FAILURE: {agent_name} generated fallback content!")
                    all_passed = False
                else:
                    print(f"✅ {agent_name} content appears high-quality")
                    
            else:
                print(f"⚠️ {agent_name} failed cleanly: {result.error_message}")
                print(f"✅ GOOD: No fallback content from {agent_name}")
                
        except Exception as e:
            print(f"⚠️ {agent_name} raised exception: {e}")
            print(f"✅ GOOD: Clean exception from {agent_name}")
    
    return all_passed


async def test_llm_manager_directly():
    """Test the RobustLLMManager directly"""
    
    print("\n🔧 Testing RobustLLMManager Directly")
    print("=" * 45)
    
    # Test with invalid endpoint to ensure clean failure
    invalid_endpoints = [
        LLMEndpoint(
            url="http://nonexistent-server:9999",
            model="fake-model",
            timeout=2,
            max_retries=2
        )
    ]
    
    llm_manager = RobustLLMManager(endpoints=invalid_endpoints)
    
    from langchain_core.messages import SystemMessage, HumanMessage
    
    messages = [
        SystemMessage(content="You are a test assistant."),
        HumanMessage(content="Generate test content.")
    ]
    
    try:
        print("🔄 Attempting LLM call with invalid endpoint...")
        response = await llm_manager.generate_content(messages)
        print(f"❌ UNEXPECTED: Got response from invalid endpoint: {response}")
        return False
        
    except Exception as e:
        print(f"✅ GOOD: LLM manager failed cleanly: {type(e).__name__}")
        print("✅ No fallback content generated")
        return True


async def validate_no_fallback_methods():
    """Validate that no fallback methods exist in the codebase"""
    
    print("\n🔍 Validating No Fallback Methods Exist")
    print("=" * 45)
    
    import inspect
    from services.proposal.multi_agent import (
        coordinator_agent, cover_letter_specialist, 
        technical_specialist, management_specialist
    )
    
    modules_to_check = [
        coordinator_agent,
        cover_letter_specialist, 
        technical_specialist,
        management_specialist
    ]
    
    fallback_methods_found = []
    
    for module in modules_to_check:
        for name, obj in inspect.getmembers(module):
            if inspect.isclass(obj):
                for method_name, method in inspect.getmembers(obj):
                    if 'fallback' in method_name.lower():
                        fallback_methods_found.append(f"{module.__name__}.{name}.{method_name}")
    
    if fallback_methods_found:
        print("❌ FAILURE: Found fallback methods:")
        for method in fallback_methods_found:
            print(f"   - {method}")
        return False
    else:
        print("✅ GOOD: No fallback methods found in agent classes")
        return True


async def main():
    """Main test function"""
    
    print("🚀 Testing: No Fallbacks in Multi-Agent System")
    print("=" * 60)
    print("This test validates that the system fails cleanly when LLM is")
    print("unavailable, rather than generating low-quality fallback content.")
    print("=" * 60)
    
    test_results = []
    
    # Test 1: Workflow with unavailable LLM
    try:
        result1 = await test_workflow_with_unavailable_llm()
        test_results.append(("Workflow Clean Failure", result1))
    except Exception as e:
        print(f"❌ Workflow test failed: {e}")
        test_results.append(("Workflow Clean Failure", False))
    
    # Test 2: Individual agents
    try:
        result2 = await test_individual_agents()
        test_results.append(("Individual Agents", result2))
    except Exception as e:
        print(f"❌ Individual agents test failed: {e}")
        test_results.append(("Individual Agents", False))
    
    # Test 3: LLM Manager directly
    try:
        result3 = await test_llm_manager_directly()
        test_results.append(("LLM Manager", result3))
    except Exception as e:
        print(f"❌ LLM Manager test failed: {e}")
        test_results.append(("LLM Manager", False))
    
    # Test 4: Validate no fallback methods
    try:
        result4 = await validate_no_fallback_methods()
        test_results.append(("No Fallback Methods", result4))
    except Exception as e:
        print(f"❌ Fallback methods validation failed: {e}")
        test_results.append(("No Fallback Methods", False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in test_results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} | {test_name}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✅ The multi-agent system properly fails cleanly")
        print("✅ No fallback content is generated")
        print("🎯 High-quality content only!")
    else:
        print("❌ SOME TESTS FAILED!")
        print("⚠️ Fallback content may still be present")
        print("🔧 Review failed tests and fix issues")
    
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
