from typing import Dict, List, Any, Optional
from loguru import logger
from services.research.semantic_scholar_service import SemanticScholarService
from langchain_ollama import ChatOllama
import re
import json


class ResearchEnhancementService:
    """
    Service for enhancing proposal content with academic research from Semantic Scholar.
    """
    
    def __init__(
        self,
        semantic_scholar_api_key: Optional[str] = None,
        llm_api_url: str = "http://ai.kontratar.com:11434"
    ):
        self.semantic_scholar = SemanticScholarService(api_key=semantic_scholar_api_key)
        self.llm = ChatOllama(model="gemma3:27b", temperature=0.0, base_url=llm_api_url)
    
    async def enhance_section_with_research(
        self,
        section_title: str,
        section_content: str,
        opportunity_context: str = "",
        max_papers: int = 5
    ) -> Dict[str, Any]:
        """
        Enhance a proposal section with relevant academic research.
        
        Args:
            section_title: Title of the proposal section
            section_content: Current content of the section
            opportunity_context: Context about the opportunity
            max_papers: Maximum number of research papers to include
            
        Returns:
            Dictionary with enhanced content and research metadata
        """
        logger.info(f"Enhancing section '{section_title}' with academic research")
        
        # Extract research topics from section content and context
        research_topics = await self._extract_research_topics(
            section_title, section_content, opportunity_context
        )
        
        if not research_topics:
            logger.warning(f"No research topics identified for section: {section_title}")
            return {
                "enhanced_content": section_content,
                "research_added": False,
                "research_topics": [],
                "papers_found": 0,
                "enhancement_summary": "No research topics identified for enhancement"
            }
        
        logger.info(f"Identified {len(research_topics)} research topics: {research_topics}")
        
        # Search for relevant research
        research_results = {}
        total_papers = 0
        
        for topic in research_topics:
            research = await self.semantic_scholar.search_relevant_research(
                topic=topic,
                limit=max_papers // len(research_topics) + 1
            )
            research_results[topic] = research
            total_papers += research["papers_found"]
        
        if total_papers == 0:
            logger.warning(f"No research papers found for topics: {research_topics}")
            return {
                "enhanced_content": section_content,
                "research_added": False,
                "research_topics": research_topics,
                "papers_found": 0,
                "enhancement_summary": "No relevant research papers found"
            }
        
        # Generate enhanced content
        enhanced_content = await self._generate_enhanced_content(
            section_title, section_content, research_results
        )
        
        # Generate enhancement summary
        enhancement_summary = self._create_enhancement_summary(research_results, total_papers)
        
        return {
            "enhanced_content": enhanced_content,
            "research_added": True,
            "research_topics": research_topics,
            "papers_found": total_papers,
            "research_results": research_results,
            "enhancement_summary": enhancement_summary
        }
    
    async def _extract_research_topics(
        self,
        section_title: str,
        section_content: str,
        opportunity_context: str
    ) -> List[str]:
        """Extract relevant research topics from section content."""
        
        system_prompt = """
You are an expert at identifying research topics that would enhance government proposal content.

Your task is to analyze the provided proposal section and identify 2-4 specific research topics that would:
1. Support the technical approach with academic evidence
2. Demonstrate state-of-the-art knowledge
3. Strengthen the credibility of the proposed solution
4. Show awareness of current research trends

Return ONLY a JSON array of research topic strings, each 2-5 words long.
Focus on technical, methodological, or domain-specific topics that would have academic research.

Example output: ["machine learning algorithms", "cybersecurity frameworks", "data visualization techniques"]
"""

        user_prompt = f"""
Analyze this proposal section and identify research topics for academic enhancement:

**Section Title:** {section_title}

**Section Content:**
{section_content[:1500]}

**Opportunity Context:**
{opportunity_context[:500]}

Identify 2-4 specific research topics that would enhance this content with academic credibility.
Return as JSON array of topic strings.
"""

        try:
            messages = [
                ("system", system_prompt),
                ("human", user_prompt)
            ]
            result = self.llm.invoke(messages)
            content = str(result.content).strip()
            
            # Extract JSON from response
            json_match = re.search(r'\[.*?\]', content, re.DOTALL)
            if json_match:
                topics = json.loads(json_match.group())
                if isinstance(topics, list):
                    # Filter and clean topics
                    cleaned_topics = [
                        topic.strip() for topic in topics 
                        if isinstance(topic, str) and len(topic.strip()) > 3
                    ]
                    return cleaned_topics[:4]  # Limit to 4 topics
            
            logger.warning("Could not extract valid research topics from LLM response")
            return []
            
        except Exception as e:
            logger.error(f"Error extracting research topics: {e}")
            # Fallback: extract topics from section title
            return self._extract_fallback_topics(section_title)
    
    def _extract_fallback_topics(self, section_title: str) -> List[str]:
        """Fallback method to extract topics from section title."""
        title_lower = section_title.lower()
        
        # Common technical areas that might have research
        topic_keywords = {
            "technical": ["technical approach", "software engineering"],
            "management": ["project management", "agile methodology"],
            "security": ["cybersecurity", "information security"],
            "data": ["data analytics", "data science"],
            "cloud": ["cloud computing", "distributed systems"],
            "ai": ["artificial intelligence", "machine learning"],
            "testing": ["software testing", "quality assurance"],
            "integration": ["system integration", "api design"]
        }
        
        topics = []
        for category, keywords in topic_keywords.items():
            if category in title_lower:
                topics.extend(keywords[:1])  # Add first keyword from category
        
        return topics[:2] if topics else ["software development"]
    
    async def _generate_enhanced_content(
        self,
        section_title: str,
        original_content: str,
        research_results: Dict[str, Any]
    ) -> str:
        """Generate enhanced content incorporating research findings."""
        
        # Prepare research context
        research_context = ""
        for topic, result in research_results.items():
            if result["papers_found"] > 0:
                research_context += f"\n--- Research on {topic} ---\n"
                research_context += result["summary"]
                
                # Add top papers
                for paper in result["papers"][:2]:  # Top 2 papers per topic
                    research_context += f"\nKey Paper: {paper['title']} ({paper['year']})\n"
                    if paper.get("abstract"):
                        abstract = paper["abstract"][:300] + "..." if len(paper["abstract"]) > 300 else paper["abstract"]
                        research_context += f"Abstract: {abstract}\n"
        
        system_prompt = """
You are a government proposal expert specializing in incorporating academic research to strengthen technical content.

Your task is to enhance the provided proposal section by:
1. Integrating relevant research findings naturally into the content
2. Adding credible academic references where appropriate
3. Strengthening technical arguments with research-backed evidence
4. Maintaining the professional government proposal tone
5. Ensuring all additions directly support the proposal objectives

**CRITICAL REQUIREMENTS:**
- Keep the original structure and key points intact
- Add research insights that directly support the technical approach
- Use phrases like "Recent research demonstrates..." or "Academic studies show..."
- Do NOT add placeholder citations or fake references
- Maintain government proposal formatting and style
- Focus on how research supports the proposed solution
"""

        user_prompt = f"""
Enhance this proposal section by incorporating the provided research findings:

**Section Title:** {section_title}

**Original Content:**
{original_content}

**Available Research:**
{research_context}

**Instructions:**
1. Enhance the original content by weaving in relevant research insights
2. Add research-backed statements that strengthen the technical approach
3. Maintain the original structure while adding academic credibility
4. Ensure all research additions directly support the proposal goals
5. Keep the government proposal tone and formatting

Generate the enhanced section content:
"""

        try:
            messages = [
                ("system", system_prompt),
                ("human", user_prompt)
            ]
            result = self.llm.invoke(messages)
            enhanced_content = str(result.content).strip()
            
            logger.info(f"Generated enhanced content ({len(enhanced_content)} characters)")
            return enhanced_content
            
        except Exception as e:
            logger.error(f"Error generating enhanced content: {e}")
            return original_content  # Return original if enhancement fails
    
    def _create_enhancement_summary(
        self, 
        research_results: Dict[str, Any], 
        total_papers: int
    ) -> str:
        """Create a summary of the research enhancement."""
        
        summary = f"Research Enhancement Applied:\n"
        summary += f"- Total research papers incorporated: {total_papers}\n"
        summary += f"- Research areas covered: {len(research_results)}\n\n"
        
        for topic, result in research_results.items():
            if result["papers_found"] > 0:
                summary += f"**{topic.title()}:**\n"
                summary += f"  - {result['papers_found']} relevant papers found\n"
                
                if result["papers"]:
                    top_paper = result["papers"][0]
                    summary += f"  - Key reference: {top_paper['title']} ({top_paper['year']})\n"
                    if top_paper.get("citation_count", 0) > 10:
                        summary += f"  - High-impact paper ({top_paper['citation_count']} citations)\n"
                
                summary += "\n"
        
        return summary
    
    async def enhance_multiple_sections(
        self,
        sections: List[Dict[str, str]],
        opportunity_context: str = "",
        max_papers_per_section: int = 3
    ) -> List[Dict[str, Any]]:
        """
        Enhance multiple proposal sections with research.
        
        Args:
            sections: List of section dictionaries with 'title' and 'content'
            opportunity_context: Context about the opportunity
            max_papers_per_section: Maximum papers per section
            
        Returns:
            List of enhanced section results
        """
        logger.info(f"Enhancing {len(sections)} sections with research")
        
        enhanced_sections = []
        
        for section in sections:
            title = section.get("title", "")
            content = section.get("content", "")
            
            if not title or not content:
                logger.warning("Skipping section with missing title or content")
                enhanced_sections.append({
                    "enhanced_content": content,
                    "research_added": False,
                    "error": "Missing title or content"
                })
                continue
            
            try:
                enhanced_result = await self.enhance_section_with_research(
                    section_title=title,
                    section_content=content,
                    opportunity_context=opportunity_context,
                    max_papers=max_papers_per_section
                )
                enhanced_sections.append(enhanced_result)
                
            except Exception as e:
                logger.error(f"Error enhancing section '{title}': {e}")
                enhanced_sections.append({
                    "enhanced_content": content,
                    "research_added": False,
                    "error": str(e)
                })
        
        return enhanced_sections
