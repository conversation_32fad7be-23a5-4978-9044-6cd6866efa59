from typing import Any, Dict
from services.proposal.utilities import ProposalUtilities
from services.chroma.chroma_service import ChromaService
from database import get_kontratar_db
from langchain_ollama import ChatOllama
from loguru import logger

class KeyPersonnelService:
    """
    Service for generating key personnel requirements context and LLM output using ChromaDB and an LLM.
    """
    def __init__(
        self,
        embedding_api_url: str = "http://ai.kontratar.com:5000",
        llm_api_url: str = "http://ai.kontratar.com:11434",
    ):
        self.chroma_service = ChromaService(embedding_api_url, None)
        #self.llm = KontratarLLM(api_url=llm_api_url, api_key=None)
        self.llm = ChatOllama(model="gemma3:27b", num_ctx = 5000, temperature=0.0, base_url=llm_api_url)

    async def extract_key_personnel(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
    ) -> Dict[str, Any]:
        """
        Generate key personnel requirements output using ChromaDB and LLM.
        Returns a dict with 'content' (parsed JSON) and 'context' (list of cleaned chunks).
        Raises an exception if a valid JSON is not produced after 3 attempts.
        """

        logger.info(f"Extracting key personnel for opportunity_id={opportunity_id}, tenant_id={tenant_id}, source={source}")

        chroma_query = '''
            Return excerpts from government solicitation documents that specify key personnel requirements including:
            - Required job titles or positions
            - Key personnel qualifications
            - Staffing requirements
            - Personnel experience requirements
            - Key personnel resumes or CVs needed
            - Project manager, technical lead, or other critical roles
        '''
        
        async for db in get_kontratar_db():
            max_chunks = 5
            collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
            logger.info(f"Using collection_name: '{collection_name}' for ChromaDB retrieval")
            relevant_chunks = await self.chroma_service.get_relevant_chunks(
                db, collection_name, chroma_query, n_results=max_chunks
            )
            logger.info(f"Retrieved {len(relevant_chunks)} relevant chunks from ChromaDB for key personnel extraction")
            requirements_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]
            logger.debug(f"Requirements context chunks: {requirements_context}")
            context_str = "\n".join(requirements_context)
            logger.debug(f"Context string for LLM prompt: {context_str[:500]}{'...' if len(context_str) > 500 else ''}")
            break
        
        system_prompt = '''
        Role: Government Solicitation Key Personnel Expert
        Task: Generate a JSON response with key personnel requirements.

        Rules:
        1. **Output Format** :
            {{
                "resumes_needed": boolean,
                "personnel": [
                    {{
                        "title": string,
                        "responsibilities": string
                    }}
                ]
            }}

            - Use the information found in <context> to generate the key personnel requirements in this structure.
            - "resumes_needed" should be true if the solicitation explicitly requires resumes, CVs, or personnel qualifications.
            - "personnel" should contain an array of objects, each with:
                - "title": the job title or position mentioned in the solicitation (e.g., "Project Manager").
                - "responsibilities": a concise summary of the key responsibilities or duties for that role, as described or implied in the solicitation.

            Example Response:
            {{
                "resumes_needed": true,
                "personnel": [
                    {{
                        "title": "Project Manager",
                        "responsibilities": "Oversees project execution, manages team deliverables, and serves as the primary point of contact."
                    }},
                    {{
                        "title": "Technical Lead",
                        "responsibilities": "Leads technical solution development and ensures compliance with technical requirements."
                    }},
                    {{
                        "title": "Senior Developer",
                        "responsibilities": "Designs and implements software components and supports integration efforts."
                    }},
                    {{
                        "title": "Quality Assurance Specialist",
                        "responsibilities": "Ensures deliverables meet quality standards and conducts testing and validation."
                    }}
                ]
            }}
        '''
        user_prompt = f'''
        Generate key personnel requirements for this solicitation in JSON format using the following context:

        <context>
            {context_str}
        </context>

        **Constraints**:

        - DO NOT add any text before or after the JSON requirements, just generate the requirement in the correct structure
        - For "resumes_needed", set to true if the document mentions resumes, CVs, personnel qualifications, or key personnel submissions
        - For "personnel", extract all job titles, positions, or roles mentioned as required or key personnel
        - Use clear, professional job titles (e.g., "Project Manager", "Technical Lead", "Senior Developer")
        - If no specific personnel requirements are found, return an empty array for personnel and false for resumes_needed

        **Defaults**:
        - "resumes_needed": false
        - "personnel": []
        ''' 

        logger.info("Constructed system and user prompts for LLM key personnel extraction")
        logger.debug(f"System prompt: {system_prompt}")
        logger.debug(f"User prompt: {user_prompt[:500]}{'...' if len(user_prompt) > 500 else ''}")

        messages = [
            ("system", system_prompt),
            ("human", user_prompt)
        ]
        logger.info("Invoking LLM for key personnel extraction")
        result = self.llm.invoke(messages)
        logger.info("LLM invocation complete")
        logger.debug(f"LLM raw result: {str(result.content)[:500]}{'...' if len(str(result.content)) > 500 else ''}")
        
        personnel = ProposalUtilities.extract_json_from_brackets(str(result.content))
        logger.info(f"Extracted personnel JSON: {personnel}")

        return personnel if personnel is not None else {}

    @staticmethod
    def is_valid_key_personnel_requirements(data: dict) -> bool:
        try:
            # Check if all required keys are present
            required_keys = ["resumes_needed", "personnel"]
            for key in required_keys:
                if key not in data:
                    return False
            
            # Validate resumes_needed is boolean
            if not isinstance(data["resumes_needed"], bool):
                return False
            
            # Validate personnel is a list
            if not isinstance(data["personnel"], list):
                return False
            
            # Validate all personnel items are strings
            for item in data["personnel"]:
                if not isinstance(item, str):
                    return False
            
            return True
        except Exception:
            return False