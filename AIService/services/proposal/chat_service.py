import asyncio
import json
from typing import Any, Dict, Iterator, List, Optional, AsyncGenerator

from langchain_core.messages import BaseMessageChunk, BaseMessage, HumanMessage, SystemMessage
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.runnables import Runnable, RunnablePassthrough
from langchain.chains import create_retrieval_chain
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain.schema import Document
from services.chroma.chroma_service import ChromaService
from database import get_kontratar_db
from langchain_ollama import ChatOllama
from loguru import logger

class ChatService:
    """
    Service for question answering using ChromaDB and LLM with streaming and non-streaming options.
    """
    def __init__(
        self,
        embedding_api_url: str = "http://ai.kontratar.com:5000",
        llm_api_url: str = "http://ai.kontratar.com:11434",
    ):
        self.chroma_service = ChromaService(embedding_api_url, None)
        #self.llm = KontratarLLM(api_url=llm_api_url, api_key=None)
        self.llm = ChatOllama(model="gemma3:27b", temperature=0.0, base_url=llm_api_url)

    async def _get_relevant_chunks(
        self,
        question: str,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        max_chunks: int
    ) -> List[str]:
        """Get relevant chunks using existing ChromaService."""
        async for db in get_kontratar_db():
            collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
            logger.info(f"Using collection_name: '{collection_name}' for ChromaDB retrieval")
            
            relevant_chunks = await self.chroma_service.get_relevant_chunks(
                db, collection_name, question, n_results=max_chunks
            )

            logger.info(f"Chunks: {relevant_chunks}")
            
            # Clean chunks
            cleaned_chunks = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]
            logger.info(f"Retrieved {len(cleaned_chunks)} relevant chunks from ChromaDB")
            
            return cleaned_chunks

    async def _create_document_chain(self) -> Runnable[dict[str, Any], Any]:
        """Create RAG chain with document combination."""

        SYSTEM_TEMPLATE = """
            Role: 
            Government Solicitation Expert Assistant
            
            Task: 
            You will be given a question to answer about a government opportunity. YOU MUST answer the question sspecified using the provided context.

            Rules:
            1. **Answer Format**: Provide clear, concise, and accurate answers based on the context
            2. **Source Attribution**: Base your answers primarily on the provided context
            3. **Professional Tone**: Use professional and formal language appropriate for government documents
            4. **Accuracy**: If the context doesn't contain enough information to answer the question, say so
            5. **Structure**: Organize your response logically with clear sections if needed

            <context>
            {context}
            </context>
        """

        question_answering_prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    SYSTEM_TEMPLATE,
                ),
                MessagesPlaceholder(variable_name="messages"),
            ]
        )

        # Create document chain
        document_chain = create_stuff_documents_chain(self.llm, question_answering_prompt)
        
        return document_chain

    
    async def _get_single_response(
        self, 
        rag_chain, 
        question: str, 
        context_chunks: List[str]
    ) -> str:
        """Get a single response from the RAG chain."""
        try:
            logger.info("Executing RAG chain for single response")
            
            # Execute the chain
            result = await rag_chain.ainvoke({"question": question})
            
            logger.info("RAG chain execution completed")
            return result
            
        except Exception as e:
            logger.error(f"Error in single response: {e}")
            raise
    
    async def get_answer(
        self,
        question: str,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        streaming: bool = False,
        max_chunks: int = 3,
    ) -> Iterator[BaseMessageChunk] | str:
        """
        Get an answer to a question using ChromaDB and LLM.
        
        Args:
            question: The question to answer
            opportunity_id: The opportunity ID
            tenant_id: The tenant ID
            source: The source type ("custom" or other)
            max_chunks: Maximum number of relevant chunks to retrieve
            streaming: Whether to return streaming response or not
            
        Returns:
            Dict with 'answer' and 'context' keys
        """
        
        # Add logs for tracing execution and debugging
        logger.info(f"Received question: '{question}' for opportunity_id: '{opportunity_id}', tenant_id: '{tenant_id}', source: '{source}', streaming: {streaming}, max_chunks: {max_chunks}")
        
        # Get relevant chunks using existing ChromaService
        relevant_chunks = await self._get_relevant_chunks(
            question, opportunity_id, tenant_id, source, max_chunks
        )
        
        # Convert chunks to LangChain Documents
        documents = [Document(page_content=chunk) for chunk in relevant_chunks]
        
        system_prompt = '''
        Role: 
        Government Solicitation Expert Assistant
        
        Task: 
        You will be given a question to answer about a government opportunity. YOU MUST answer the question sspecified using the provided context.

        Rules:
        1. **Answer Format**: Provide clear, concise, and accurate answers based on the context
        2. **Source Attribution**: Base your answers primarily on the provided context
        3. **Professional Tone**: Use professional and formal language appropriate for government documents
        4. **Accuracy**: If the context doesn't contain enough information to answer the question, say so
        5. **Structure**: Organize your response logically with clear sections if needed
        '''
        

        
        logger.info("Constructed system prompt and user prompt for LLM")
        
        if streaming:
            logger.info("Requesting streaming response from LLM")
            result = await self._get_streaming_response(question, documents)
            logger.info("Streaming response initiated")
            return result
        else:
            logger.info("Requesting non-streaming response from LLM")
            result = await self._get_non_streaming_response(system_prompt, "")
            logger.info("Non-streaming response received")
            return result

    async def _get_non_streaming_response(
        self, 
        system_prompt: str, 
        user_prompt: str, 
    ) -> str:
        """
        Get a non-streaming response from the LLM.
        """
        logger.info("Invoking LLM for non-streaming response")
        messages = [
            ("system", system_prompt),
            ("human", user_prompt)
        ]
        response = self.llm.invoke(messages)
        logger.info("LLM invocation complete for non-streaming response")
        logger.debug(f"LLM response content: {str(response.content)[:500]}{'...' if len(str(response.content)) > 500 else ''}")
        return str(response.content)

    async def _get_streaming_response(
        self, 
        query: str,
        context_docs: List[Document] = []
    ) -> Iterator[Any]:
        """
        Get a streaming response from the LLM.
        """
        logger.info("Invoking LLM for streaming response")
        document_chain = await self._create_document_chain()
        response_stream = document_chain.stream({
            "context": context_docs,
            "messages": [
                HumanMessage(content=query)
            ]
        })
        return response_stream

    async def get_conversation_history(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Retrieve conversation history for a specific opportunity.
        This is a placeholder - implement based on your database schema.
        """
        # TODO: Implement conversation history retrieval
        # This would typically query your database for previous Q&A pairs
        return []

    async def save_conversation(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        question: str,
        answer: str,
        context_chunks: List[str]
    ) -> bool:
        """
        Save a conversation exchange to the database.
        This is a placeholder - implement based on your database schema.
        """
        # TODO: Implement conversation saving
        # This would typically save the Q&A pair to your database
        return True

    @staticmethod
    def validate_question(question: str) -> bool:
        """
        Validate if the question is appropriate for the chat service.
        """
        if not question or not question.strip():
            return False
        
        # Add any additional validation rules here
        # For example, check for minimum length, inappropriate content, etc.
        
        return True