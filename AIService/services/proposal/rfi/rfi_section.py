import logging
from typing import Optional

from langchain_ollama import ChatOllama

from controllers.kontratar.chromadb_mapping_controller import ChromaDBMappingController
from database import get_kontratar_db
from services.chroma.chroma_service import ChromaService

class RFISection:
    def __init__(
        self,
        opportunity_metadata: str,
        technical_requirements: str,
        tenant_id: str,
        client: str,
        tenant_metadata: str,
        grading_criteria: str,
        internet_search_results: Optional[str] = None,
        source_documents_context: Optional[str] = None,
        profile_id: Optional[str] = None,
        personality: Optional[str] = None
    ):
        self.opportunity_metadata = opportunity_metadata
        self.technical_requirements = technical_requirements
        self.tenant_id = tenant_id
        self.client = client
        self.profile_id = profile_id
        self.tenant_metadata = tenant_metadata
        self.grading_criteria = grading_criteria
        self.internet_search_results = internet_search_results or ""
        self.source_documents_context = source_documents_context or ""
        self.personality = personality
        self.logger = logging.getLogger(self.__class__.__name__)

        self.MAX_TOKENS = 4096
        self.llm = ChatOllama(
            model="gemma3:27b",
            base_url="http://ai.kontratar.com:11434",
            num_predict=self.MAX_TOKENS,
            temperature=0
        )
        self.chroma_service = ChromaService("http://ai.kontratar.com:11434")

        self.system_prompt = f"""
            **Role:**
            You are a government contracting specialist with over 15+ years experience in writing detailed and not generic 
            Request for Information (RFI) for government opportunities.

            **Task:**
            Your job is to create a section of an RFI for a company using their company information and the
            requirements of a government opportunity. In order to successfuly create a RFI section, you MUST use the requirements for the opportunity,
            the section name and subsections, context in extra documents needed to source knowledge from,
            context about the section name and current subsection title, AS WELL AS the company information.
            Everything about the opporrtunity like Opportunity name, agency and point of contact are found in <opportunity-details>.
            The information about the company related to the RFI is found in <company-information>, section name and subsection title
            and the requirements is found in <requirements>, and extra documents to source knowledge from in <documents>.
            The evaluation criteria which indicates what would like to be seen in the RFI will be supplied in <grading-criteria>.
            You will also be possibly given relevant data from an internet search over multiple queries, which will be found in <internet-search-results>.
            The metadata about the comany like company name, point of contact and the likes are found in <company-metadata>.
            The extra documents and/or internet search results MAY or MAY not be present SO ENSURE you still generate correctly REGARDLESS of that.
            You are NOT creating a draft, you are creating the DETAILED content.

            {self.personality}

            **Instructions:**
            - DO NOT ask any follow-up questions in the text you generate.
            - You are generating a DETAILED RFI for submission.
            - DO NOT leave placeholders, refer back to context to get any infomation you need
            - When referring to requirements, DO NOT be ambigious by mentioning Requirements 5 or Requirement 8, etc. State what the
            requirement actually says, so that the reader is aware of what you are referring to.
            - DO NOT repeat or mention metadata/information in the opportunity details unless the requirements explicitly says you should.
            - Make mention of SPECIFIC technologies when talking about technical concepts.
            - When talking about the company, DO NOT use words like "Our company", "Us", "We" or nay other pronouns. Always use the Company Name/Tenant Name.
            - DO NOT include HTML tags for ANY REASON.
            - When referring to this RFI, DO NOT use words like "Proposal" or "Our Proposal", always use the term, Request for Information or RFI.
            - DO NOT use the word "Proposal" IN THIS RFI for ANY REASON.
            - DO NOT leave PLACEHOLDERS for ANY REASON.
            - DO NOT include placeholder text like [Company Name] anyhwere, for ANY REASON. If there is information you CANNOT deduce, USE a made-up BUT reasonable value.
            - DO NOT in ANY WAY or FORM list out or explain what the vendor or agency is looking for or requires, that is NOT the purpose of this RFI.
            - DO NOT explain the prupose of any section, YOUR JOB is to act to generate content for this RFI based on the company information given to you.
            - When using metrics, DO NOT use X percent or Y percent, YOU MUST use an actual number (like 23 percent), IF you canot find one from the context
            given to you, USE a made-up BUT reasonable number.
            - Information like company size can be inferred from given information in <company-information>, for example if company is deemed to be a large compnay then company count can be 1000 plus.
            - While you are free to use made-up metrics, DO NOT generate data that is NON-FACTUAL, for example saying the company/tenant has a certain certification when they do not,
            your entire work will be deemed irrelevant in that case.
            - In the RFI you generate, DO NOT make/list references to ANY of the information given in <internet-search-results>, <requirements>, <grading-criteria> or <documents>
            - In the RFI section you are generating only use relevnt data found in <internet-search-results>, <requirements>, <grading-criteria> and <documents> to
            generate that section.
        """

    async def generate_rfi_subsection(self, title: str, description: str, outline_guide: str):
        chroma_query = self.generate_chroma_query(outline_guide)
        client_collection = f"{self.tenant_id}_{self.client}" if self.profile_id is None else \
        f"{self.tenant_id}_{self.client}.{self.profile_id}"
        

        # Get company information context
        async for db in get_kontratar_db():
            company_information = await self.chroma_service.get_relevant_chunks(
                db=db, 
                collection_name=client_collection,
                query=chroma_query,
                n_results=15
            )

        user_prompt = f"""
            <opportunity-details>
                {self.opportunity_metadata}
            </opportunity-details>

            <company-metadata>
                {self.tenant_metadata}
            </company-metadata>

            <company-information>
                {company_information}
            </company-information>

            <requirements>
                {self.technical_requirements}
            </requirements>

            <grading-criteria>
                {self.grading_criteria}
            </grading-criteria>

            <internet-search-results>
                {self.internet_search_results}
            </internet-search-results>

            Title of this subsection is: {title}

            Description of this subsection is: {description}

            Leverage all provided details—including opportunity information, company data, and specific requirements—to craft a comprehensive, persuasive, and fully compliant
            proposal section for this opportunity. Ensure your output integrates every relevant element seamlessly.

            Return as a SINGLE JSON object.
            Your JSON object MUST have the title, subtitle, paragraphs fields.

            The title field SHOULD be the title of the subsection you are addressing.
            The subtitle field SHOULD provide a more detailed explanation that expands on the title's meaning.
            The paragraphs field MUST be an ARRAY of STRINGS with AT LEAST 3 items and with each array item containing AT LEAST 4 sentences that thoroughly
            explain the subsection's key points.
        """

        messages = [
            {"role": "system", "content": self.system_prompt.strip()},
            {"role": "user", "content": user_prompt.strip()},
        ]
        response = self.llm.invoke(messages, max_tokens=self.MAX_TOKENS)
        return str(response.content) if hasattr(response, "content") else str(response)

    async def generate_rfi_section_header(self, title: str, description: str, outline_guide: str):
        chroma_query = self.generate_chroma_query(outline_guide)
        
        client_collection = f"{self.tenant_id}_{self.client}" if self.profile_id is None else \
        f"{self.tenant_id}_{self.client}.{self.profile_id}"
        
        # self.chroma_service.set_mapping_repository(self.mapping_repository)  # If needed
        async for db in get_kontratar_db():
            company_information = await self.chroma_service.get_relevant_chunks(
                db=db, 
                collection_name=client_collection,
                query=chroma_query,
                n_results=5
            )
            break

        complete_client_context = f"{self.tenant_metadata}\n\n{company_information}"

        system_prompt = """
        **Role:** 
        You are a government contracting specialist with over 15+ years experience in writing detailed and not generic 
        Request for Information (RFI) for government opportunities.

        **Task:**
        Your job is to create the header of a section of an RFI for a company using their company information and the
        requirements of a governemnt opportunity. This is NOT a draft, BUT the DETAILED content.
        In order to successfuly create the header of the proposal section, you MUST use:
        1. The requirements for the opportunity,
        2. The section name and description
        3. The outline guide that specifies what should be seen in this section
        4. Company information that has information relevant to the outline guide.

        The company information is found in <company-information>, the outline guide in <guide> and the requirements 
        is found in <requirements>.

        **Important**:
        The header SHOULD be returned as JSON so ensure to generate accurate, parsebale and valid JSON.
        You will be given a JSON schema to comply to, ensure to follow it strictly.
        """

        user_prompt = f"""
            <guide>
                {outline_guide}
            </guide>

            <opportunity-details>
                {self.opportunity_metadata}
            </opportunity-details>

            <company-information>
                {complete_client_context}
            </company-information>

            <requirements>
                {self.technical_requirements}
            </requirements>

            Title of this section is: {title}

            Description of this section is: {description}

            Use the JSON schema below:
            {{ "title": "string", "subtitle": "string", "paragraph": "string", "number": "string" }}

            - "title" the title of the section you are addressing.
            - "subtitle" WILL provide a more detailed explanation that expands on the title's meaning.
            - "paragraph" WILL contain at least 6 sentences that thoroughly explain the section's key points.
            - "number" is be the EXACT number in the table of contents this header is addressing, eg 1.0, 3.0

            **Important:**
            - YOU MUST return ALL specified fields
            - ONLY return A COMPLIANT JSON, DO NOT return any other thing.
        """

        messages = [
            {"role": "system", "content": system_prompt.strip()},
            {"role": "user", "content": user_prompt.strip()},
        ]
        response = self.llm.invoke(messages)
        return str(response.content) if hasattr(response, "content") else str(response)

    def generate_subsection_image_text(self, subsection_content: str):
        system_prompt = """
        You are a highly specialized image alt text generator.
        Your job is to generate alt text for an image that will describe a section of the proposal.
        This text should fully describe the image as the image will be places at the end of this section.
        The text will be passed to an image generation model so ensure the text fully descibes the image.

        The section content is found in <section-content>.

        ENSURE the alt text is descriptive and to the point, and is no more than 100 characters.
        """

        user_prompt = f"""
            Here is the section content:
            <section-content>
                {subsection_content}
            </section-content>

            Generate a text for an image that will desribe this section.
        """
        messages = [
            {"role": "system", "content": system_prompt.strip()},
            {"role": "user", "content": user_prompt.strip()},
        ]
        response = self.llm.invoke(messages, max_tokens=75)
        return str(response.content) if hasattr(response, "content") else str(response)

    
    def generate_chroma_query(self, text: str, is_client: bool = True):
        """
        This generate a query for teh vector database using teh information from the outline,
        or another provided piece of text. Specify is_client to True if you want a query geared 
        towards getting client information.
        """
        
        if not text:
            return ""

        llm = ChatOllama(model="gemma3:27b", base_url="http://ai.kontratar.com:11434")
        if is_client:
            prompt = (
                "Given the following RFP section, generate a concise search query that would retrieve the most relevant company information from a vector database collection. "
                "The query should focus on company background, experience, capabilities, and any details relevant to the requirements and topics in the guide below.\n\n"
                f"{text}\n\n"
                "Search Query:"
            )
        else:
            prompt = (
                "Given the following RFP section, generate a concise search query that would retrieve the most relevant opportunity-specific information from a vector database collection. "
                "The query should focus on opportunity requirements, government needs, evaluation criteria, and any details relevant to the requirements and topics in the guide below.\n\n"
                f"{text}\n\n"
                "Search Query:"
            )
        response = llm.invoke(prompt)
        return str(response.content)
        