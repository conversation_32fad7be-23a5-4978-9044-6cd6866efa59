import json
from typing import Dict, Any, Optional
from loguru import logger
from enum import Enum


class ProposalDestination(Enum):
    """Enum for proposal destination routing."""
    REVIEW = "review"
    FORMAT = "format"


class WorkflowRoutingService:
    """
    Service for managing proposal workflow routing decisions.
    Ensures proposals are correctly routed to ProposalsInReview or ProposalsFormatQueue
    based on job instructions.
    """
    
    @staticmethod
    def parse_job_instruction(job_instruction: str) -> Dict[str, Any]:
        """
        Parse and validate job instruction JSON.
        
        Args:
            job_instruction: JSON string containing job parameters
            
        Returns:
            Parsed job instruction dictionary
            
        Raises:
            ValueError: If job instruction is invalid
        """
        try:
            job = json.loads(job_instruction)
            logger.info(f"Parsed job instruction with keys: {list(job.keys())}")
            return job
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in job instruction: {e}")
            raise ValueError(f"Invalid job instruction JSON: {e}")
    
    @staticmethod
    def determine_routing_destination(job_instruction: str) -> ProposalDestination:
        """
        Determine where the proposal should be routed based on job instruction.
        
        Args:
            job_instruction: JSON string containing job parameters
            
        Returns:
            ProposalDestination enum indicating where to route the proposal
        """
        job = WorkflowRoutingService.parse_job_instruction(job_instruction)
        
        # Check for setForReview flag (primary routing decision)
        set_for_review = job.get("setForReview")
        
        if set_for_review is None:
            logger.warning("setForReview flag not found in job instruction, defaulting to review")
            return ProposalDestination.REVIEW
        
        if isinstance(set_for_review, bool):
            destination = ProposalDestination.REVIEW if set_for_review else ProposalDestination.FORMAT
            logger.info(f"Routing destination determined: {destination.value} (setForReview={set_for_review})")
            return destination
        
        # Handle string values
        if isinstance(set_for_review, str):
            set_for_review_lower = set_for_review.lower()
            if set_for_review_lower in ["true", "1", "yes"]:
                logger.info("Routing to review (setForReview string evaluated as true)")
                return ProposalDestination.REVIEW
            elif set_for_review_lower in ["false", "0", "no"]:
                logger.info("Routing to format (setForReview string evaluated as false)")
                return ProposalDestination.FORMAT
        
        logger.warning(f"Invalid setForReview value: {set_for_review}, defaulting to review")
        return ProposalDestination.REVIEW
    
    @staticmethod
    def validate_job_instruction(job_instruction: str) -> Dict[str, Any]:
        """
        Validate job instruction contains required fields for proposal generation.
        
        Args:
            job_instruction: JSON string containing job parameters
            
        Returns:
            Dictionary with validation results
        """
        try:
            job = WorkflowRoutingService.parse_job_instruction(job_instruction)
        except ValueError as e:
            return {
                "valid": False,
                "errors": [str(e)],
                "missing_fields": [],
                "warnings": []
            }
        
        # Required fields for proposal generation
        required_fields = [
            "opportunityId",
            "tenantId", 
            "clientShortName",
            "opportunityType"
        ]
        
        # Optional but important fields
        important_fields = [
            "setForReview",
            "profileId",
            "aiPersonalityId"
        ]
        
        errors = []
        missing_fields = []
        warnings = []
        
        # Check required fields
        for field in required_fields:
            if field not in job:
                missing_fields.append(field)
                errors.append(f"Missing required field: {field}")
            elif job[field] is None:
                errors.append(f"Required field {field} is null")
        
        # Check important fields
        for field in important_fields:
            if field not in job:
                warnings.append(f"Missing optional field: {field}")
            elif job[field] is None:
                warnings.append(f"Optional field {field} is null")
        
        # Validate specific field values
        if "setForReview" in job:
            set_for_review = job["setForReview"]
            if not isinstance(set_for_review, (bool, str, int)):
                warnings.append(f"setForReview has unexpected type: {type(set_for_review)}")
        
        # Check for export-related fields when setForReview is false
        if job.get("setForReview") is False:
            if "exportType" not in job or job["exportType"] is None:
                warnings.append("exportType not specified for format queue routing")
            if "coverPage" not in job:
                warnings.append("coverPage not specified for format queue routing")
        
        is_valid = len(errors) == 0
        
        validation_result = {
            "valid": is_valid,
            "errors": errors,
            "missing_fields": missing_fields,
            "warnings": warnings,
            "routing_destination": WorkflowRoutingService.determine_routing_destination(job_instruction).value if is_valid else None
        }
        
        logger.info(f"Job instruction validation result: valid={is_valid}, errors={len(errors)}, warnings={len(warnings)}")
        return validation_result
    
    @staticmethod
    def get_routing_parameters(job_instruction: str) -> Dict[str, Any]:
        """
        Extract parameters needed for routing the proposal to the correct destination.
        
        Args:
            job_instruction: JSON string containing job parameters
            
        Returns:
            Dictionary with routing parameters
        """
        job = WorkflowRoutingService.parse_job_instruction(job_instruction)
        destination = WorkflowRoutingService.determine_routing_destination(job_instruction)
        
        # Common parameters for both destinations
        common_params = {
            "opportunity_id": job.get("opportunityId"),
            "tenant_id": job.get("tenantId"),
            "client_short_name": job.get("clientShortName"),
            "opportunity_type": job.get("opportunityType"),
            "job_instruction": job_instruction
        }
        
        # Destination-specific parameters
        if destination == ProposalDestination.REVIEW:
            routing_params = {
                **common_params,
                "destination": "review",
                "volume_number": job.get("volumeNumber", 1),
                "section_number": job.get("sectionNumber", "1")
            }
        else:  # FORMAT
            routing_params = {
                **common_params,
                "destination": "format",
                "format_type": job.get("exportType", 1),
                "cover_page": job.get("coverPage"),
                "job_submitted_by": job.get("jobSubmittedBy", "system")
            }
        
        logger.info(f"Generated routing parameters for {destination.value} destination")
        return routing_params
    
    @staticmethod
    def create_enhanced_job_instruction(
        base_instruction: str,
        enhancements: Dict[str, Any] = None
    ) -> str:
        """
        Create an enhanced job instruction with additional parameters.
        
        Args:
            base_instruction: Original job instruction JSON string
            enhancements: Additional parameters to add/override
            
        Returns:
            Enhanced job instruction JSON string
        """
        job = WorkflowRoutingService.parse_job_instruction(base_instruction)
        
        if enhancements:
            job.update(enhancements)
            logger.info(f"Enhanced job instruction with: {list(enhancements.keys())}")
        
        # Ensure critical fields have defaults
        defaults = {
            "setForReview": True,  # Default to review if not specified
            "generatedVolumes": [1, 2, 3, 4, 5],  # Default volumes
            "isRFP": True  # Default to RFP
        }
        
        for key, default_value in defaults.items():
            if key not in job:
                job[key] = default_value
                logger.info(f"Added default value for {key}: {default_value}")
        
        return json.dumps(job)
    
    @staticmethod
    def log_routing_decision(job_instruction: str, additional_context: Dict[str, Any] = None):
        """
        Log the routing decision for audit and debugging purposes.
        
        Args:
            job_instruction: JSON string containing job parameters
            additional_context: Additional context for logging
        """
        try:
            job = WorkflowRoutingService.parse_job_instruction(job_instruction)
            destination = WorkflowRoutingService.determine_routing_destination(job_instruction)
            
            log_data = {
                "opportunity_id": job.get("opportunityId"),
                "tenant_id": job.get("tenantId"),
                "routing_destination": destination.value,
                "set_for_review": job.get("setForReview"),
                "opportunity_type": job.get("opportunityType"),
                "timestamp": job.get("timestamp", "not_provided")
            }
            
            if additional_context:
                log_data.update(additional_context)
            
            logger.info(f"WORKFLOW_ROUTING: {log_data}")
            
        except Exception as e:
            logger.error(f"Error logging routing decision: {e}")
    
    @staticmethod
    def get_workflow_status_summary(job_instruction: str) -> Dict[str, Any]:
        """
        Get a comprehensive summary of the workflow routing status.
        
        Args:
            job_instruction: JSON string containing job parameters
            
        Returns:
            Dictionary with workflow status summary
        """
        validation = WorkflowRoutingService.validate_job_instruction(job_instruction)
        
        if not validation["valid"]:
            return {
                "status": "invalid",
                "can_proceed": False,
                "validation": validation,
                "routing_destination": None,
                "summary": f"Job instruction validation failed: {', '.join(validation['errors'])}"
            }
        
        destination = WorkflowRoutingService.determine_routing_destination(job_instruction)
        routing_params = WorkflowRoutingService.get_routing_parameters(job_instruction)
        
        return {
            "status": "valid",
            "can_proceed": True,
            "validation": validation,
            "routing_destination": destination.value,
            "routing_parameters": routing_params,
            "summary": f"Proposal will be routed to {destination.value} workflow"
        }
