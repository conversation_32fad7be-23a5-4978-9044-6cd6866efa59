services:
  aiservice:
    build:
      context: ./AIService
      dockerfile: Dockerfile.test
    image: kontratar/aiservice:${TAG:-latest}
    container_name: aiservice-app
    ports:
      - "${APP_PORT:-3011}:${APP_PORT:-3011}"
    environment:
      - APP_HOST=${APP_HOST:-0.0.0.0}
      - APP_PORT=${APP_PORT:-3011}
      - DEBUG=${DEBUG:-false}
      # Database Configuration - Kontratar Database
      - KONTRATAR_DB_HOST=${KONTRATAR_DB_HOST:-localhost}
      - KONTRATAR_DB_PORT=${KONTRATAR_DB_PORT:-5432}
      - KONTRATAR_DB_NAME=${KONTRATAR_DB_NAME:-postgres}
      - KONTRATAR_DB_USER=${KONTRATAR_DB_USER:-postgres}
      - KONTRATAR_DB_PASSWORD=${KONTRATAR_DB_PASSWORD:-password}
      # Database Configuration - Customer Database
      - CUSTOMER_DB_HOST=${CUSTOMER_DB_HOST:-localhost}
      - CUSTOMER_DB_PORT=${CUSTOMER_DB_PORT:-5433}
      - CUSTOMER_DB_NAME=${CUSTOMER_DB_NAME:-postgres}
      - CUSTOMER_DB_USER=${CUSTOMER_DB_USER:-postgres}
      - CUSTOMER_DB_PASSWORD=${CUSTOMER_DB_PASSWORD:-password}
      # LangChain Configuration
      - LANGCHAIN_TRACING_V2=${LANGCHAIN_TRACING_V2:-false}
      - LANGCHAIN_API_KEY=${LANGCHAIN_API_KEY:-your_api_key_here}
      - LANGCHAIN_PROJECT=${LANGCHAIN_PROJECT:-kontratar}
      # ChromaDB Configuration
      - CHROMADB_PORT_1=${CHROMADB_PORT_1:-8000}
      - CHROMADB_PORT_2=${CHROMADB_PORT_2:-8001}
      - CHROMADB_PORT_3=${CHROMADB_PORT_3:-8002}
      - CHROMADB_PORT_4=${CHROMADB_PORT_4:-8003}
      - CHROMADB_PORT_5=${CHROMADB_PORT_5:-8004}
      - CHROMADB_PROTOCOL=${CHROMADB_PROTOCOL:-http}
      - CHROMADB_SERVER_NAME=${CHROMADB_SERVER_NAME:-localhost}
    env_file:
      - ./AIService/.env  # Optional: create this file for local overrides
    networks:
      - aiservice-network
    restart: unless-stopped
    volumes:
      - aiservice-logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://${APP_HOST:-0.0.0.0}:${APP_PORT:-3011}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  aiservice-logs:
    driver: local

networks:
  aiservice-network:
    driver: bridge